import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { useRFQStore } from "@/store/rfqStore";

export function TimelinePanel({ rfqId }: { rfqId: string }) {
  const rfq = useRFQStore(s => s.rfqs.find(r => r.id === rfqId));
  if (!rfq) return null;
  return (
    <Card>
      <CardHeader>
        <CardTitle>History</CardTitle>
      </CardHeader>
      <CardContent>
        <ol className="space-y-3">
          {rfq.history.length === 0 && <p className="text-sm text-muted-foreground">No history yet</p>}
          {rfq.history.map(h => (
            <li key={h.id} className="flex items-start gap-2">
              <div className="mt-1 h-2 w-2 rounded-full bg-primary" />
              <div>
                <div className="text-sm font-medium">{h.action}</div>
                <div className="text-xs text-muted-foreground">{new Date(h.at).toLocaleString()} {h.user ? `• ${h.user}` : ''}</div>
                {h.details && <div className="text-sm">{h.details}</div>}
              </div>
            </li>
          ))}
        </ol>
      </CardContent>
    </Card>
  );
}

