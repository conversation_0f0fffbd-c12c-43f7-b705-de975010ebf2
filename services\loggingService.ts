import { writeFileSync, existsSync, mkdirSync, appendFileSync } from 'fs';
import { join } from 'path';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4
}

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: any;
  workflowId?: string;
  stepId?: string;
  duration?: number;
  error?: Error;
}

interface WorkflowMetrics {
  workflowId: string;
  startTime: number;
  endTime?: number;
  status: 'running' | 'completed' | 'failed' | 'terminated';
  totalSteps: number;
  completedSteps: number;
  failedSteps: number;
  errors: string[];
  stepMetrics: Map<string, StepMetric>;
}

interface StepMetric {
  stepId: string;
  stepName: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'timeout';
  attempts: number;
  error?: string;
}

export class LoggingService {
  private static instance: LoggingService;
  private logLevel: LogLevel = LogLevel.INFO;
  private logDir: string;
  private workflowMetrics: Map<string, WorkflowMetrics> = new Map();

  constructor(logDir: string = 'logs') {
    this.logDir = logDir;
    this.ensureLogDirectory();
  }

  static getInstance(logDir?: string): LoggingService {
    if (!LoggingService.instance) {
      LoggingService.instance = new LoggingService(logDir);
    }
    return LoggingService.instance;
  }

  private ensureLogDirectory(): void {
    if (!existsSync(this.logDir)) {
      mkdirSync(this.logDir, { recursive: true });
    }
  }

  setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel;
  }

  private formatLogEntry(entry: LogEntry): string {
    const levelName = LogLevel[entry.level];
    const contextStr = entry.context ? ` | Context: ${JSON.stringify(entry.context)}` : '';
    const workflowStr = entry.workflowId ? ` | Workflow: ${entry.workflowId}` : '';
    const stepStr = entry.stepId ? ` | Step: ${entry.stepId}` : '';
    const durationStr = entry.duration ? ` | Duration: ${entry.duration}ms` : '';
    const errorStr = entry.error ? ` | Error: ${entry.error.message}` : '';

    return `[${entry.timestamp}] ${levelName}: ${entry.message}${workflowStr}${stepStr}${durationStr}${contextStr}${errorStr}\n`;
  }

  private writeToFile(entry: LogEntry): void {
    const logFile = join(this.logDir, `procurement-agent-${new Date().toISOString().split('T')[0]}.log`);
    const formattedEntry = this.formatLogEntry(entry);
    
    try {
      appendFileSync(logFile, formattedEntry);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  private log(level: LogLevel, message: string, context?: any, workflowId?: string, stepId?: string, duration?: number, error?: Error): void {
    if (!this.shouldLog(level)) return;

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      workflowId,
      stepId,
      duration,
      error
    };

    // Console output with colors
    const levelColors = {
      [LogLevel.DEBUG]: '\x1b[36m', // Cyan
      [LogLevel.INFO]: '\x1b[32m',  // Green
      [LogLevel.WARN]: '\x1b[33m',  // Yellow
      [LogLevel.ERROR]: '\x1b[31m', // Red
      [LogLevel.CRITICAL]: '\x1b[35m' // Magenta
    };

    const color = levelColors[level] || '';
    const reset = '\x1b[0m';
    console.log(`${color}${this.formatLogEntry(entry).trim()}${reset}`);

    // Write to file
    this.writeToFile(entry);
  }

  debug(message: string, context?: any, workflowId?: string, stepId?: string): void {
    this.log(LogLevel.DEBUG, message, context, workflowId, stepId);
  }

  info(message: string, context?: any, workflowId?: string, stepId?: string): void {
    this.log(LogLevel.INFO, message, context, workflowId, stepId);
  }

  warn(message: string, context?: any, workflowId?: string, stepId?: string): void {
    this.log(LogLevel.WARN, message, context, workflowId, stepId);
  }

  error(message: string, error?: Error, context?: any, workflowId?: string, stepId?: string): void {
    this.log(LogLevel.ERROR, message, context, workflowId, stepId, undefined, error);
  }

  critical(message: string, error?: Error, context?: any, workflowId?: string, stepId?: string): void {
    this.log(LogLevel.CRITICAL, message, context, workflowId, stepId, undefined, error);
  }

  // Workflow-specific logging methods
  startWorkflow(workflowId: string, input: string): void {
    const metrics: WorkflowMetrics = {
      workflowId,
      startTime: Date.now(),
      status: 'running',
      totalSteps: 0,
      completedSteps: 0,
      failedSteps: 0,
      errors: [],
      stepMetrics: new Map()
    };

    this.workflowMetrics.set(workflowId, metrics);
    this.info(`🚀 Workflow started`, { input: input.substring(0, 100) }, workflowId);
  }

  startStep(workflowId: string, stepId: string, stepName: string): void {
    const workflow = this.workflowMetrics.get(workflowId);
    if (!workflow) return;

    const stepMetric: StepMetric = {
      stepId,
      stepName,
      startTime: Date.now(),
      status: 'running',
      attempts: 1
    };

    workflow.stepMetrics.set(stepId, stepMetric);
    workflow.totalSteps = Math.max(workflow.totalSteps, workflow.stepMetrics.size);
    
    this.info(`▶️ Step started: ${stepName}`, undefined, workflowId, stepId);
  }

  completeStep(workflowId: string, stepId: string, duration: number, result?: any): void {
    const workflow = this.workflowMetrics.get(workflowId);
    if (!workflow) return;

    const stepMetric = workflow.stepMetrics.get(stepId);
    if (stepMetric) {
      stepMetric.endTime = Date.now();
      stepMetric.duration = duration;
      stepMetric.status = 'completed';
      workflow.completedSteps++;
    }

    this.info(`✅ Step completed: ${stepMetric?.stepName}`, { result: result ? 'Success' : 'No result' }, workflowId, stepId, duration);
  }

  failStep(workflowId: string, stepId: string, error: Error, attempts: number): void {
    const workflow = this.workflowMetrics.get(workflowId);
    if (!workflow) return;

    const stepMetric = workflow.stepMetrics.get(stepId);
    if (stepMetric) {
      stepMetric.status = 'failed';
      stepMetric.attempts = attempts;
      stepMetric.error = error.message;
      stepMetric.endTime = Date.now();
      stepMetric.duration = stepMetric.endTime - stepMetric.startTime;
      workflow.failedSteps++;
      workflow.errors.push(`${stepId}: ${error.message}`);
    }

    this.error(`❌ Step failed: ${stepMetric?.stepName}`, error, { attempts }, workflowId, stepId);
  }

  completeWorkflow(workflowId: string, status: 'completed' | 'failed' | 'terminated', result?: any): void {
    const workflow = this.workflowMetrics.get(workflowId);
    if (!workflow) return;

    workflow.endTime = Date.now();
    workflow.status = status;
    const totalDuration = workflow.endTime - workflow.startTime;

    const summary = {
      status,
      totalDuration,
      totalSteps: workflow.totalSteps,
      completedSteps: workflow.completedSteps,
      failedSteps: workflow.failedSteps,
      successRate: workflow.totalSteps > 0 ? (workflow.completedSteps / workflow.totalSteps * 100).toFixed(1) + '%' : '0%'
    };

    if (status === 'completed') {
      this.info(`🎉 Workflow completed successfully`, summary, workflowId);
    } else {
      this.error(`💥 Workflow ${status}`, undefined, summary, workflowId);
    }

    // Save detailed metrics to file
    this.saveWorkflowMetrics(workflowId, workflow);
  }

  private saveWorkflowMetrics(workflowId: string, metrics: WorkflowMetrics): void {
    try {
      const metricsFile = join(this.logDir, `workflow-metrics-${workflowId}.json`);
      const detailedMetrics = {
        ...metrics,
        stepMetrics: Array.from(metrics.stepMetrics.entries()).map(([id, metric]) => ({ id, ...metric }))
      };
      
      writeFileSync(metricsFile, JSON.stringify(detailedMetrics, null, 2));
      this.debug(`📊 Workflow metrics saved to ${metricsFile}`, undefined, workflowId);
    } catch (error) {
      this.error('Failed to save workflow metrics', error instanceof Error ? error : new Error(String(error)), undefined, workflowId);
    }
  }

  getWorkflowMetrics(workflowId: string): WorkflowMetrics | undefined {
    return this.workflowMetrics.get(workflowId);
  }

  getAllWorkflowMetrics(): WorkflowMetrics[] {
    return Array.from(this.workflowMetrics.values());
  }

  // Health check and system status
  getSystemStatus(): {
    activeWorkflows: number;
    totalWorkflows: number;
    logLevel: string;
    logDirectory: string;
    uptime: number;
  } {
    const activeWorkflows = Array.from(this.workflowMetrics.values()).filter(w => w.status === 'running').length;
    
    return {
      activeWorkflows,
      totalWorkflows: this.workflowMetrics.size,
      logLevel: LogLevel[this.logLevel],
      logDirectory: this.logDir,
      uptime: process.uptime() * 1000
    };
  }
}
