import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";
import { PropsWithChildren } from "react";

export function ActionButton({ loading, className, ...props }: PropsWithChildren<{ loading?: boolean } & React.ComponentProps<typeof Button>>) {
  return (
    <Button className={cn(className)} disabled={props.disabled || loading} {...props}>
      {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
      {props.children}
    </Button>
  );
}

