import { useMemo, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Plus, Trash, Upload } from "lucide-react";
import type { PurchaseRequisition, PurchaseRequisitionLine, UUID } from "@/types/p2p";

export function RequisitionForm({
  onSubmit,
  initial,
}: {
  onSubmit: (req: PurchaseRequisition) => void;
  initial?: Partial<PurchaseRequisition>;
}) {
  const [lines, setLines] = useState<PurchaseRequisitionLine[]>(
    initial?.lines ?? [
      {
        id: crypto.randomUUID(),
        description: "",
        quantity: 1,
        unitPrice: 0,
        currency: "INR",
      },
    ]
  );
  const [justification, setJustification] = useState(initial?.justification ?? "");
  const [attachments, setAttachments] = useState<{ name: string; url?: string }[]>(
    initial?.attachments ?? []
  );

  const totals = useMemo(() => {
    const amount = lines.reduce((sum, l) => sum + l.quantity * l.unitPrice, 0);
    return { amount, currency: lines[0]?.currency ?? "INR" };
  }, [lines]);

  const addLine = () =>
    setLines((prev) => [
      ...prev,
      { id: crypto.randomUUID(), description: "", quantity: 1, unitPrice: 0, currency: "INR" },
    ]);

  const removeLine = (id: UUID) => setLines((prev) => prev.filter((l) => l.id !== id));

  const handleSubmit = () => {
    const req: PurchaseRequisition = {
      id: crypto.randomUUID(),
      requesterId: "user-1",
      departmentId: "dept-1",
      justification,
      status: "submitted",
      lines,
      attachments,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      totalAmount: totals.amount,
      currency: totals.currency,
    };
    onSubmit(req);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Requisition Creation</CardTitle>
        <CardDescription>
          Create a purchase requisition: item search or free-text, line items, attachments. Auto totals included.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Lines */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-base">Line Items</Label>
            <Button variant="outline" size="sm" onClick={addLine}>
              <Plus className="h-4 w-4 mr-2" /> Add line
            </Button>
          </div>

          <div className="space-y-3">
            {lines.map((line) => (
              <div key={line.id} className="grid grid-cols-12 gap-2 items-end">
                <div className="col-span-5">
                  <Label>Description</Label>
                  <Input
                    placeholder="Search item or free-text description"
                    value={line.description}
                    onChange={(e) =>
                      setLines((prev) =>
                        prev.map((l) => (l.id === line.id ? { ...l, description: e.target.value } : l))
                      )
                    }
                  />
                </div>
                <div className="col-span-2">
                  <Label>Qty</Label>
                  <Input
                    type="number"
                    min={1}
                    value={line.quantity}
                    onChange={(e) =>
                      setLines((prev) =>
                        prev.map((l) => (l.id === line.id ? { ...l, quantity: Number(e.target.value || 0) } : l))
                      )
                    }
                  />
                </div>
                <div className="col-span-3">
                  <Label>Unit Price</Label>
                  <Input
                    type="number"
                    min={0}
                    value={line.unitPrice}
                    onChange={(e) =>
                      setLines((prev) =>
                        prev.map((l) => (l.id === line.id ? { ...l, unitPrice: Number(e.target.value || 0) } : l))
                      )
                    }
                  />
                </div>
                <div className="col-span-1">
                  <Label>Currency</Label>
                  <Input
                    value={line.currency}
                    onChange={(e) =>
                      setLines((prev) =>
                        prev.map((l) => (l.id === line.id ? { ...l, currency: e.target.value.toUpperCase() } : l))
                      )
                    }
                  />
                </div>
                <div className="col-span-1 flex justify-end">
                  <Button variant="ghost" size="icon" onClick={() => removeLine(line.id)}>
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Totals */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">Auto-calculated totals</div>
          <div className="text-xl font-semibold">
            {totals.currency} {totals.amount.toLocaleString()}
          </div>
        </div>

        <Separator />

        {/* Justification */}
        <div>
          <Label>Business Justification</Label>
          <Textarea
            placeholder="Explain the need, expected benefits, and any constraints"
            value={justification}
            onChange={(e) => setJustification(e.target.value)}
          />
        </div>

        {/* Attachments */}
        <div className="space-y-2">
          <Label>Attachments</Label>
          <div className="flex items-center gap-2">
            <Button variant="outline" type="button">
              <Upload className="h-4 w-4 mr-2" /> Upload
            </Button>
            <div className="text-sm text-muted-foreground">PDFs, images, docs</div>
          </div>
          {attachments.length > 0 && (
            <ul className="list-disc pl-5 text-sm text-muted-foreground">
              {attachments.map((a) => (
                <li key={a.name}>{a.name}</li>
              ))}
            </ul>
          )}
        </div>

        {/* Submit */}
        <div className="flex justify-end">
          <Button onClick={handleSubmit}>Submit for approval</Button>
        </div>
      </CardContent>
    </Card>
  );
}

