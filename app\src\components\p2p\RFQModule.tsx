import { useMemo, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import type { RequestForQuote, Vendor, VendorProposal, UUID } from "@/types/p2p";

export type RFQModuleProps = {
  vendors: Vendor[];
  rfqs: RequestForQuote[];
  proposals: VendorProposal[];
  onCreateRFQ: (rfq: RequestForQuote) => void;
  onSubmitProposal: (proposal: VendorProposal) => void;
};

export function RFQModule({ vendors, rfqs, proposals, onCreateRFQ, onSubmitProposal }: RFQModuleProps) {
  const [form, setForm] = useState<{ requisitionId: string; productIds: string; deadline: string; invitedVendorIds: string }>(() => ({
    requisitionId: "",
    productIds: "",
    deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10),
    invitedVendorIds: "",
  }));

  const rfqWithProposals = useMemo(() => {
    const byId: Record<UUID, VendorProposal[]> = {} as any;
    for (const p of proposals) {
      byId[p.rfqId] ||= [];
      byId[p.rfqId].push(p);
    }
    return rfqs.map(r => ({
      rfq: r,
      proposals: byId[r.id] || [],
      best: (byId[r.id] || []).slice().sort((a, b) => a.price - b.price)[0],
    }));
  }, [rfqs, proposals]);

  function handleCreateRFQ(e: React.FormEvent) {
    e.preventDefault();
    if (!form.requisitionId.trim()) return;
    const id = crypto.randomUUID();
    const productIds = form.productIds
      .split(",")
      .map(s => s.trim())
      .filter(Boolean);
    const invitedVendorIds = form.invitedVendorIds
      .split(",")
      .map(s => s.trim())
      .filter(Boolean);
    onCreateRFQ({ id, requisitionId: form.requisitionId.trim(), productIds, deadline: form.deadline, invitedVendorIds });
    setForm(f => ({ ...f, requisitionId: "", productIds: "", invitedVendorIds: "" }));
  }

  function handleSubmitProposal(e: React.FormEvent<HTMLFormElement>, rfqId: UUID) {
    e.preventDefault();
    const fd = new FormData(e.currentTarget);
    const vendorId = String(fd.get("vendorId") || "");
    const price = Number(fd.get("price") || 0);
    const currency = String(fd.get("currency") || "INR");
    if (!vendorId || !price) return;
    onSubmitProposal({ id: crypto.randomUUID(), rfqId, vendorId, price, currency });
    e.currentTarget.reset();
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>RFQ / RFP</CardTitle>
        <CardDescription>Create RFQs, invite vendors and track proposals</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <form onSubmit={handleCreateRFQ} className="grid grid-cols-1 md:grid-cols-6 gap-3">
          <Input placeholder="Requisition ID" value={form.requisitionId} onChange={e => setForm({ ...form, requisitionId: e.target.value })} className="md:col-span-2" />
          <Input placeholder="Product IDs (comma separated)" value={form.productIds} onChange={e => setForm({ ...form, productIds: e.target.value })} className="md:col-span-2" />
          <Input type="date" value={form.deadline} onChange={e => setForm({ ...form, deadline: e.target.value })} className="md:col-span-1" />
          <Input placeholder="Vendor IDs (comma separated)" value={form.invitedVendorIds} onChange={e => setForm({ ...form, invitedVendorIds: e.target.value })} className="md:col-span-3" />
          <Button type="submit" className="md:col-span-1">Create RFQ</Button>
        </form>

        <Separator />

        <div className="space-y-6">
          {rfqWithProposals.length === 0 ? (
            <p className="text-muted-foreground">No RFQs yet. Create one above.</p>
          ) : (
            rfqWithProposals.map(({ rfq, proposals, best }) => (
              <div key={rfq.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">RFQ #{rfq.id.slice(0, 8)}</div>
                    <div className="text-xs text-muted-foreground">Req: {rfq.requisitionId} • Deadline: {new Date(rfq.deadline).toLocaleDateString()}</div>
                  </div>
                  <div className="flex items-center gap-2">
                    {best && <Badge variant="secondary">Best: {best.price} {best.currency}</Badge>}
                    <Badge>{proposals.length} proposals</Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  {proposals.length === 0 ? (
                    <p className="text-sm text-muted-foreground">No proposals yet.</p>
                  ) : (
                    <ul className="text-sm grid grid-cols-1 md:grid-cols-2 gap-2">
                      {proposals.map(p => {
                        const v = vendors.find(v => v.id === p.vendorId);
                        return (
                          <li key={p.id} className="border rounded-md p-2 flex items-center justify-between">
                            <span>{v?.name || p.vendorId}</span>
                            <span className="font-mono">{p.price} {p.currency}</span>
                          </li>
                        );
                      })}
                    </ul>
                  )}
                </div>

                <form onSubmit={e => handleSubmitProposal(e, rfq.id)} className="grid grid-cols-1 md:grid-cols-5 gap-2">
                  <select name="vendorId" className="border rounded-md px-3 py-2 bg-transparent">
                    <option value="">Select vendor</option>
                    {vendors.map(v => (
                      <option key={v.id} value={v.id}>{v.name}</option>
                    ))}
                  </select>
                  <Input name="price" type="number" step="0.01" placeholder="Price" />
                  <Input name="currency" placeholder="Currency" defaultValue="INR" />
                  <Textarea name="terms" placeholder="Terms (optional)" className="md:col-span-2" />
                  <Button type="submit">Submit Proposal</Button>
                </form>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}

