import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Mail, FileText, Calendar, User, Building, Phone, MapPin } from "lucide-react";
import type { RFQ } from "@/store/rfqStore";

interface RFQScrollableContainerProps {
  rfq: RFQ;
  onSendEmail: (rfq: RFQ) => void;
  isLoading?: boolean;
}

export function RFQScrollableContainer({ rfq, onSendEmail, isLoading = false }: RFQScrollableContainerProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">Request for Quotation</CardTitle>
          </div>
          <Badge variant="outline" className="text-xs">
            {rfq.status}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <div className="rfq-scroll-container">
          <ScrollArea className="h-[400px] px-6">
            <div className="space-y-6">
              {/* RFQ Header Information */}
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold text-sm text-muted-foreground mb-2">RFQ Details</h3>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">RFQ No.:</span>
                        <span className="text-sm">{rfq.refNumber}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Date of Issue:</span>
                        <span className="text-sm">{formatDate(rfq.createdAt)}</span>
                      </div>
                      {rfq.responseDeadline && (
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium">Response Deadline:</span>
                          <span className="text-sm">{formatDate(rfq.responseDeadline)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {rfq.client && (
                    <div>
                      <h3 className="font-semibold text-sm text-muted-foreground mb-2">Client Information</h3>
                      <div className="space-y-2">
                        {rfq.client.company && (
                          <div className="flex items-center gap-2">
                            <Building className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">{rfq.client.company}</span>
                          </div>
                        )}
                        {rfq.client.contactName && (
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">{rfq.client.contactName}</span>
                          </div>
                        )}
                        {rfq.client.email && (
                          <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">{rfq.client.email}</span>
                          </div>
                        )}
                        {rfq.client.phone && (
                          <div className="flex items-center gap-2">
                            <Phone className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">{rfq.client.phone}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {/* RFQ Title and Description */}
              <div className="space-y-3">
                <h2 className="text-xl font-semibold">{rfq.title}</h2>
                {rfq.description && (
                  <div className="prose prose-sm max-w-none">
                    <p className="text-muted-foreground leading-relaxed">{rfq.description}</p>
                  </div>
                )}
              </div>

              <Separator />

              {/* Line Items */}
              {rfq.lineItems && rfq.lineItems.length > 0 && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-lg">Product Specifications</h3>
                  <div className="space-y-4">
                    {rfq.lineItems.map((item, index) => (
                      <div key={item.id} className="border rounded-lg p-4 space-y-3">
                        <div className="flex items-start justify-between">
                          <div className="space-y-2 flex-1">
                            <div className="flex items-center gap-2">
                              <Badge variant="secondary" className="text-xs">
                                Item #{index + 1}
                              </Badge>
                              <h4 className="font-medium">{item.itemName}</h4>
                            </div>
                            
                            {item.partNumber && (
                              <div className="text-sm text-muted-foreground">
                                <span className="font-medium">Part Number:</span> {item.partNumber}
                              </div>
                            )}
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                              <div>
                                <span className="font-medium">Quantity:</span> {item.quantity} {item.unit || 'units'}
                              </div>
                              {item.dueDate && (
                                <div>
                                  <span className="font-medium">Due Date:</span> {formatDate(item.dueDate)}
                                </div>
                              )}
                            </div>
                            
                            {item.materialSpec && (
                              <div className="text-sm">
                                <span className="font-medium">Material Specification:</span>
                                <p className="text-muted-foreground mt-1">{item.materialSpec}</p>
                              </div>
                            )}
                            
                            {item.notes && (
                              <div className="text-sm">
                                <span className="font-medium">Notes:</span>
                                <p className="text-muted-foreground mt-1">{item.notes}</p>
                              </div>
                            )}
                          </div>
                          
                          {item.imageUrl && (
                            <div className="ml-4">
                              <img 
                                src={item.imageUrl} 
                                alt={item.itemName}
                                className="w-20 h-20 object-cover rounded border"
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Additional Notes */}
              {rfq.notes && (
                <>
                  <Separator />
                  <div className="space-y-3">
                    <h3 className="font-semibold">Additional Notes</h3>
                    <div className="prose prose-sm max-w-none">
                      <p className="text-muted-foreground leading-relaxed">{rfq.notes}</p>
                    </div>
                  </div>
                </>
              )}

              {/* Attachments */}
              {rfq.attachments && rfq.attachments.length > 0 && (
                <>
                  <Separator />
                  <div className="space-y-3">
                    <h3 className="font-semibold">Attachments</h3>
                    <div className="space-y-2">
                      {rfq.attachments.map((attachment) => (
                        <div key={attachment.id} className="flex items-center gap-2 p-2 border rounded">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{attachment.name}</span>
                          {attachment.type && (
                            <Badge variant="outline" className="text-xs">
                              {attachment.type}
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>
          </ScrollArea>
        </div>
        
        {/* Send Email Button */}
        <div className="p-6 pt-4 border-t">
          <Button 
            onClick={() => onSendEmail(rfq)}
            disabled={isLoading}
            className="w-full send-rfq-btn"
            size="lg"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                Sending...
              </>
            ) : (
              <>
                <Mail className="h-4 w-4 mr-2" />
                Send RFQ via Email
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
