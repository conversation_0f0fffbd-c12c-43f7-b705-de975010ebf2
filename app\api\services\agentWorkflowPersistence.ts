import { AgentResultService } from './agentResultService.js';
import { CreateAgentResult, UpdateAgentResult, CreateAgentExecutionStep, UpdateAgentExecutionStep } from '../database/types.js';
import crypto from 'crypto';

export interface WorkflowData {
  input: string;
  queryAnalysis?: any;
  marketResearch?: any;
  recommendations?: any;
  documents?: any;
  actionPlan?: any;
  rfqData?: any;
  gitCommitInfo?: any;
  timestamp?: string;
  error?: string;
}

export interface WorkflowStep {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'timeout';
  startTime?: number;
  endTime?: number;
  duration?: number;
  attempts: number;
  error?: string;
  result?: any;
}

export interface WorkflowStatus {
  isTerminated: boolean;
  totalDuration: number;
  totalSteps: number;
  completedSteps: number;
  failedSteps: number;
  steps: WorkflowStep[];
  isComplete: boolean;
}

export class AgentWorkflowPersistence {
  private static instance: AgentWorkflowPersistence;
  private agentResultService: AgentResultService;

  private constructor() {
    this.agentResultService = AgentResultService.getInstance();
  }

  static getInstance(): AgentWorkflowPersistence {
    if (!AgentWorkflowPersistence.instance) {
      AgentWorkflowPersistence.instance = new AgentWorkflowPersistence();
    }
    return AgentWorkflowPersistence.instance;
  }

  private generateInputHash(input: string): string {
    return crypto.createHash('sha256').update(input).digest('hex');
  }

  // Initialize workflow persistence at the start of agent execution
  async initializeWorkflow(workflowId: string, input: string, config?: any): Promise<string> {
    try {
      const createData: CreateAgentResult = {
        workflow_id: workflowId,
        input_text: input,
        input_hash: this.generateInputHash(input),
        status: 'running',
        workflow_config: config,
        region: 'Mangaluru, India',
        agent_version: '1.0.0',
      };

      const result = await this.agentResultService.createAgentResult(createData);
      console.log(`🗄️ Initialized workflow persistence for ${workflowId}`);
      return result.id;
    } catch (error) {
      console.error('❌ Failed to initialize workflow persistence:', error);
      // Don't throw - workflow should continue even if persistence fails
      return '';
    }
  }

  // Update workflow with step results
  async updateWorkflowStep(
    workflowId: string,
    stepId: string,
    stepName: string,
    stepOrder: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'timeout',
    data?: {
      startTime?: number;
      endTime?: number;
      duration?: number;
      attempts?: number;
      result?: any;
      error?: string;
      errorDetails?: any;
    }
  ): Promise<void> {
    try {
      // Get the agent result to get the result_id
      const agentResult = await this.agentResultService.getAgentResult(workflowId);
      if (!agentResult) {
        console.warn(`⚠️ Agent result not found for workflow ${workflowId}`);
        return;
      }

      // Try to update existing step first
      const existingStep = await this.agentResultService.updateExecutionStep(
        stepId,
        agentResult.id,
        {
          status: status as any,
          started_at: data?.startTime ? new Date(data.startTime) : undefined,
          completed_at: data?.endTime ? new Date(data.endTime) : undefined,
          duration_ms: data?.duration,
          attempts: data?.attempts,
          result_data: data?.result,
          error_message: data?.error,
          error_details: data?.errorDetails,
        }
      );

      // If step doesn't exist, create it
      if (!existingStep) {
        const createStepData: CreateAgentExecutionStep = {
          result_id: agentResult.id,
          workflow_id: workflowId,
          step_id: stepId,
          step_name: stepName,
          step_order: stepOrder,
          status: status as any,
          started_at: data?.startTime ? new Date(data.startTime) : undefined,
          completed_at: data?.endTime ? new Date(data.endTime) : undefined,
          duration_ms: data?.duration,
          attempts: data?.attempts || 1,
          result_data: data?.result,
          error_message: data?.error,
          error_details: data?.errorDetails,
        };

        await this.agentResultService.createExecutionStep(createStepData);
      }

      console.log(`🗄️ Updated step ${stepId} (${stepName}) with status: ${status}`);
    } catch (error) {
      console.error(`❌ Failed to update workflow step ${stepId}:`, error);
      // Don't throw - workflow should continue even if persistence fails
    }
  }

  // Update workflow with final results
  async completeWorkflow(
    workflowId: string,
    status: 'completed' | 'failed' | 'terminated',
    workflowData: WorkflowData,
    workflowStatus: WorkflowStatus,
    executionTimeMs?: number
  ): Promise<void> {
    try {
      const updateData: UpdateAgentResult = {
        status: status as any,
        completed_at: new Date(),
        execution_time_ms: executionTimeMs,
        query_analysis: workflowData.queryAnalysis,
        market_research: workflowData.marketResearch,
        recommendations: workflowData.recommendations,
        documents: workflowData.documents,
        action_plan: workflowData.actionPlan,
        rfq_data: workflowData.rfqData,
        git_commit_info: workflowData.gitCommitInfo,
        workflow_steps: workflowStatus.steps,
        total_steps: workflowStatus.totalSteps,
        completed_steps: workflowStatus.completedSteps,
        failed_steps: workflowStatus.failedSteps,
        error_message: workflowData.error,
        partial_results: status === 'failed' && (workflowData.queryAnalysis || workflowData.recommendations),
      };

      await this.agentResultService.updateAgentResult(workflowId, updateData);
      console.log(`🗄️ Completed workflow persistence for ${workflowId} with status: ${status}`);
    } catch (error) {
      console.error('❌ Failed to complete workflow persistence:', error);
      // Don't throw - workflow should complete even if persistence fails
    }
  }

  // Save partial results in case of failure
  async savePartialResults(
    workflowId: string,
    workflowData: WorkflowData,
    error: string
  ): Promise<void> {
    try {
      const updateData: UpdateAgentResult = {
        status: 'failed',
        completed_at: new Date(),
        query_analysis: workflowData.queryAnalysis,
        market_research: workflowData.marketResearch,
        recommendations: workflowData.recommendations,
        documents: workflowData.documents,
        action_plan: workflowData.actionPlan,
        error_message: error,
        partial_results: true,
      };

      await this.agentResultService.updateAgentResult(workflowId, updateData);
      console.log(`🗄️ Saved partial results for ${workflowId}`);
    } catch (persistError) {
      console.error('❌ Failed to save partial results:', persistError);
      // Don't throw - error handling should continue
    }
  }

  // Add tags to workflow result
  async addWorkflowTags(workflowId: string, tags: Array<{ name: string; value?: string; type?: 'system' | 'user' | 'custom' | 'auto' }>): Promise<void> {
    try {
      const agentResult = await this.agentResultService.getAgentResult(workflowId);
      if (!agentResult) {
        console.warn(`⚠️ Agent result not found for workflow ${workflowId}`);
        return;
      }

      for (const tag of tags) {
        await this.agentResultService.addTag({
          result_id: agentResult.id,
          tag_name: tag.name,
          tag_value: tag.value,
          tag_type: tag.type || 'auto',
        });
      }

      console.log(`🗄️ Added ${tags.length} tags to workflow ${workflowId}`);
    } catch (error) {
      console.error('❌ Failed to add workflow tags:', error);
      // Don't throw - tagging is optional
    }
  }

  // Get workflow result
  async getWorkflowResult(workflowId: string) {
    try {
      return await this.agentResultService.getAgentResult(workflowId);
    } catch (error) {
      console.error('❌ Failed to get workflow result:', error);
      return null;
    }
  }

  // Find similar workflows
  async findSimilarWorkflows(input: string, limit = 5, threshold = 0.7) {
    try {
      return await this.agentResultService.findSimilarResults(input, limit, threshold);
    } catch (error) {
      console.error('❌ Failed to find similar workflows:', error);
      return { results: [], query_embedding: [], total_found: 0 };
    }
  }

  // Get workflow statistics
  async getWorkflowStats() {
    try {
      return await this.agentResultService.getResultsStats();
    } catch (error) {
      console.error('❌ Failed to get workflow stats:', error);
      return {
        total: 0,
        by_status: {},
        recent_24h: 0,
        avg_execution_time_ms: 0,
      };
    }
  }

  // Check if database is available
  async isDatabaseAvailable(): Promise<boolean> {
    try {
      // First check if the service itself is available
      if (!this.agentResultService.isServiceAvailable()) {
        console.warn('⚠️ PostgreSQL client not available for workflow persistence');
        return false;
      }

      const stats = await this.agentResultService.getResultsStats();
      return true;
    } catch (error) {
      console.warn('⚠️ Database not available for workflow persistence:', error);
      return false;
    }
  }

  // Auto-tag workflow based on content
  async autoTagWorkflow(workflowId: string, workflowData: WorkflowData): Promise<void> {
    const tags: Array<{ name: string; value?: string; type: 'auto' }> = [];

    try {
      // Tag based on input content
      const input = workflowData.input.toLowerCase();
      
      if (input.includes('rfq') || input.includes('request for quote')) {
        tags.push({ name: 'type', value: 'rfq', type: 'auto' });
      }
      
      if (input.includes('supplier') || input.includes('vendor')) {
        tags.push({ name: 'type', value: 'supplier_search', type: 'auto' });
      }
      
      if (input.includes('procurement') || input.includes('purchase')) {
        tags.push({ name: 'category', value: 'procurement', type: 'auto' });
      }

      // Tag based on results
      if (workflowData.rfqData) {
        tags.push({ name: 'has_rfq', value: 'true', type: 'auto' });
      }

      if (workflowData.recommendations) {
        tags.push({ name: 'has_recommendations', value: 'true', type: 'auto' });
      }

      if (workflowData.error) {
        tags.push({ name: 'has_error', value: 'true', type: 'auto' });
      }

      // Add region tag
      tags.push({ name: 'region', value: 'Mangaluru', type: 'auto' });

      if (tags.length > 0) {
        await this.addWorkflowTags(workflowId, tags);
      }
    } catch (error) {
      console.error('❌ Failed to auto-tag workflow:', error);
      // Don't throw - tagging is optional
    }
  }
}
