import dotenv from "dotenv";
import { LoggingService, LogLevel } from "./services/loggingService.js";
import { AgentWorkflowPersistence } from "./app/api/services/agentWorkflowPersistence.js";
dotenv.config();

// Initialize logging service
const logger = LoggingService.getInstance();
logger.setLogLevel(LogLevel.INFO);

// Enhanced Workflow Control System
interface WorkflowConfig {
  maxSteps: number;
  stepTimeout: number;
  totalTimeout: number;
  retryAttempts: number;
  retryDelay: number;
}

interface WorkflowStep {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'timeout';
  startTime?: number;
  endTime?: number;
  duration?: number;
  attempts: number;
  error?: string;
  result?: any;
}

class WorkflowController {
  private config: WorkflowConfig;
  private steps: Map<string, WorkflowStep> = new Map();
  private startTime: number;
  private isTerminated: boolean = false;
  private persistence?: AgentWorkflowPersistence;
  private workflowId?: string;

  constructor(config: Partial<WorkflowConfig> = {}, persistence?: AgentWorkflowPersistence, workflowId?: string) {
    this.config = {
      maxSteps: config.maxSteps || 10,
      stepTimeout: config.stepTimeout || 30000, // 30 seconds per step
      totalTimeout: config.totalTimeout || 300000, // 5 minutes total
      retryAttempts: config.retryAttempts || 2,
      retryDelay: config.retryDelay || 1000
    };
    this.startTime = Date.now();
    this.persistence = persistence;
    this.workflowId = workflowId;
  }

  async executeStep<T>(
    stepId: string,
    stepName: string,
    operation: () => Promise<T>
  ): Promise<T> {
    if (this.isTerminated) {
      throw new Error('Workflow has been terminated');
    }

    // Check total timeout
    if (Date.now() - this.startTime > this.config.totalTimeout) {
      this.terminate('Total workflow timeout exceeded');
      throw new Error('Workflow timeout exceeded');
    }

    // Check max steps
    if (this.steps.size >= this.config.maxSteps) {
      this.terminate('Maximum steps exceeded');
      throw new Error('Maximum workflow steps exceeded');
    }

    const step: WorkflowStep = {
      id: stepId,
      name: stepName,
      status: 'pending',
      attempts: 0
    };

    this.steps.set(stepId, step);

    for (let attempt = 1; attempt <= this.config.retryAttempts + 1; attempt++) {
      step.attempts = attempt;
      step.status = 'running';
      step.startTime = Date.now();

      // Update persistence with step start
      if (this.persistence && this.workflowId) {
        await this.persistence.updateWorkflowStep(
          this.workflowId,
          stepId,
          stepName,
          this.steps.size,
          'running',
          { startTime: step.startTime, attempts: attempt }
        );
      }

      try {
        const result = await Promise.race([
          operation(),
          new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error('Step timeout')), this.config.stepTimeout)
          )
        ]);

        step.status = 'completed';
        step.endTime = Date.now();
        step.duration = step.endTime - step.startTime!;
        step.result = result;

        // Update persistence with step completion
        if (this.persistence && this.workflowId) {
          await this.persistence.updateWorkflowStep(
            this.workflowId,
            stepId,
            stepName,
            this.steps.size,
            'completed',
            {
              startTime: step.startTime,
              endTime: step.endTime,
              duration: step.duration,
              attempts: attempt,
              result: result
            }
          );
        }

        console.log(`✅ Step ${stepId} (${stepName}) completed in ${step.duration}ms`);
        return result;

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        step.error = errorMessage;

        if (errorMessage.includes('timeout')) {
          step.status = 'timeout';
          console.error(`⏰ Step ${stepId} (${stepName}) timed out on attempt ${attempt}`);
        } else {
          step.status = 'failed';
          console.error(`❌ Step ${stepId} (${stepName}) failed on attempt ${attempt}: ${errorMessage}`);
        }

        if (attempt <= this.config.retryAttempts) {
          console.log(`🔄 Retrying step ${stepId} in ${this.config.retryDelay}ms...`);
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
        } else {
          step.endTime = Date.now();
          step.duration = step.endTime - step.startTime!;

          // Update persistence with step failure
          if (this.persistence && this.workflowId) {
            await this.persistence.updateWorkflowStep(
              this.workflowId,
              stepId,
              stepName,
              this.steps.size,
              step.status as any,
              {
                startTime: step.startTime,
                endTime: step.endTime,
                duration: step.duration,
                attempts: attempt,
                error: errorMessage
              }
            );
          }

          throw new Error(`Step ${stepId} failed after ${attempt} attempts: ${errorMessage}`);
        }
      }
    }

    throw new Error(`Step ${stepId} failed after all retry attempts`);
  }

  terminate(reason: string): void {
    this.isTerminated = true;
    console.log(`🛑 Workflow terminated: ${reason}`);
  }

  getStatus() {
    const totalDuration = Date.now() - this.startTime;
    const completedSteps = Array.from(this.steps.values()).filter(s => s.status === 'completed').length;
    const failedSteps = Array.from(this.steps.values()).filter(s => s.status === 'failed' || s.status === 'timeout').length;

    return {
      isTerminated: this.isTerminated,
      totalDuration,
      totalSteps: this.steps.size,
      completedSteps,
      failedSteps,
      steps: Array.from(this.steps.values()),
      isComplete: this.isTerminated || completedSteps + failedSteps === this.steps.size
    };
  }
}

// LangGraph-based Procurement Agent with Perplexity Sonar Pro
interface PerplexityResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

class PerplexityAgent {
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async query(prompt: string, instructions: string): Promise<string> {
    try {
      const response = await fetch('https://api.perplexity.ai/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'sonar-pro',
          messages: [
            { role: 'system', content: instructions },
            { role: 'user', content: prompt }
          ],
          temperature: 0.7,
          max_tokens: 2000,
        }),
      });

      if (!response.ok) {
        throw new Error(`Perplexity API error: ${response.statusText}`);
      }

      const data: PerplexityResponse = await response.json();
      return data.choices[0]?.message?.content || 'No response generated';
    } catch (error) {
      console.error('Perplexity API error:', error);
      return `Error: ${error.message}`;
    }
  }
}

// Enhanced workflow state with control metadata
interface WorkflowState {
  input: string;
  queryAnalysis?: string;
  marketResearch?: string;
  recommendations?: string;
  documents?: string;
  actionPlan?: string;
  rfqData?: any;
  gitCommitInfo?: any;
  errors: string[];
  controller: WorkflowController;
  startTime: number;
  endTime?: number;
  status: 'running' | 'completed' | 'failed' | 'terminated';
}

async function procurementAgentWorkflow({ input, env }): Promise<any> {
  const workflowId = `workflow-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  const perplexityAgent = new PerplexityAgent(process.env.PERPLEXITY_API_KEY!);

  // Start workflow logging
  logger.startWorkflow(workflowId, input);

  // Initialize persistence service
  const persistence = AgentWorkflowPersistence.getInstance();
  const isDatabaseAvailable = await persistence.isDatabaseAvailable();

  if (isDatabaseAvailable) {
    console.log('🗄️ Database available - enabling workflow persistence');
  } else {
    console.log('⚠️ Database not available - workflow will run without persistence');
  }

  // Initialize enhanced workflow controller
  const controller = new WorkflowController({
    maxSteps: 8, // Limit to 8 steps maximum
    stepTimeout: 45000, // 45 seconds per step
    totalTimeout: 600000, // 10 minutes total
    retryAttempts: 2,
    retryDelay: 2000
  }, isDatabaseAvailable ? persistence : undefined, workflowId);

  // Initialize workflow state
  const state: WorkflowState = {
    input,
    errors: [],
    controller,
    startTime: Date.now(),
    status: 'running'
  };

  // Initialize workflow persistence
  if (isDatabaseAvailable) {
    await persistence.initializeWorkflow(workflowId, input, {
      maxSteps: 8,
      stepTimeout: 45000,
      totalTimeout: 600000,
      retryAttempts: 2,
      retryDelay: 2000
    });
  }

  try {
    logger.info('🚀 Starting enhanced procurement workflow', { inputLength: input.length }, workflowId);

    // Step 1: Analyze user query and determine procurement intent
    state.queryAnalysis = await controller.executeStep(
      'query-analysis',
      'Analyze user query and determine procurement intent',
      async () => {
        return await perplexityAgent.query(
          input,
          `You are a Procurement AI Agent. Analyze the user query and determine:
          1. Type of procurement request (supplier search, RFQ generation, invoice processing, etc.)
          2. Key requirements and constraints
          3. Budget considerations
          4. Regional preferences (especially for Mangaluru, India)
          5. Compliance requirements

          Respond with a structured analysis including the procurement type and key parameters.`
        );
      }
    );

    // Step 2: Retrieve relevant procurement policies and supplier data
    await controller.executeStep(
      'procurement-data',
      'Retrieve procurement policies and supplier data',
      async () => {
        // For now, use fallback data - can be enhanced with actual memory retrieval later
        const relevantMemories = [{
          text: "Fallback procurement policies: Standard approval workflows, ESG compliance required, regional supplier preferences for Mangaluru market."
        }];
        return relevantMemories;
      }
    );

    // Step 3: Search for real-time market data and supplier information
    state.marketResearch = await controller.executeStep(
      'market-research',
      'Conduct market research and supplier analysis',
      async () => {
        return await perplexityAgent.query(
          `Research market data for: ${input}`,
          `Search for current market information related to the procurement request. Include:
          1. Current market prices and trends
          2. Available suppliers (especially in Mangaluru region if relevant)
          3. Compliance and certification requirements
          4. Sustainability and ESG factors
          5. Recent industry developments

          Provide comprehensive market intelligence for informed procurement decisions.`
        );
      }
    );

    // Step 4: Generate procurement recommendations
    state.recommendations = await controller.executeStep(
      'recommendations',
      'Generate procurement recommendations',
      async () => {
        // Get the procurement data from previous step
        const procurementData = controller.getStatus().steps.find(s => s.id === 'procurement-data')?.result || [];

        const context = `
        Query Analysis: ${state.queryAnalysis}

        Internal Knowledge: ${procurementData.map((m: any) => m.text).join('\n')}

        Market Research: ${state.marketResearch}
        `;

        return await perplexityAgent.query(
          `Generate procurement recommendations based on this context: ${context}`,
          `You are an expert Procurement AI Agent. Based on the analysis and research, provide:

          1. **Supplier Recommendations**: Top 3-5 suppliers with rationale
          2. **Cost Analysis**: Budget estimates and cost-saving opportunities
          3. **Risk Assessment**: Potential risks and mitigation strategies
          4. **Compliance Check**: Regulatory and ESG compliance status
          5. **Timeline**: Recommended procurement timeline
          6. **Next Steps**: Specific actions to take (RFQ, RFP, direct purchase, etc.)
          7. **Regional Considerations**: Special considerations for Mangaluru/India market
          8. **Approval Workflow**: Required approvals based on budget and policies

          Format the response as a comprehensive procurement recommendation report.`
        );
      }
    );

    // Step 5: Generate actionable documents (RFQ, RFP, or PO draft)
    state.documents = await controller.executeStep(
      'documents',
      'Generate procurement documents',
      async () => {
        return await perplexityAgent.query(
          `Generate procurement documents for: ${input}\n\nBased on recommendations: ${state.recommendations}`,
          `Based on the procurement recommendations, generate appropriate procurement documents:

          1. If supplier search: Create RFQ (Request for Quotation) template
          2. If complex procurement: Create RFP (Request for Proposal) outline
          3. If direct purchase: Create PO (Purchase Order) draft
          4. Include all necessary terms, conditions, and specifications
          5. Ensure compliance with company policies and regional regulations
          6. Add ESG and sustainability requirements where applicable

          Format as ready-to-use procurement documents.`
        );
      }
    );

    // Step 6: Create final procurement action plan
    state.actionPlan = await controller.executeStep(
      'action-plan',
      'Create comprehensive action plan',
      async () => {
        return await perplexityAgent.query(
          `Create action plan for procurement request: ${input}

          Recommendations: ${state.recommendations}
          Documents: ${state.documents}`,
          `Create a comprehensive procurement action plan that includes:

          1. **Executive Summary**: Key findings and recommendations
          2. **Immediate Actions**: What to do next (within 24-48 hours)
          3. **Short-term Actions**: Steps for the next 1-2 weeks
          4. **Long-term Strategy**: Ongoing procurement optimization
          5. **Budget Impact**: Financial implications and savings opportunities
          6. **Risk Mitigation**: How to address identified risks
          7. **Compliance Checklist**: Ensure all regulatory requirements are met
          8. **Performance Metrics**: KPIs to track procurement success
          9. **Stakeholder Communication**: Who needs to be informed/involved
          10. **Regional Adaptations**: Specific considerations for local market

          Make it actionable and specific to the user's procurement needs.`
        );
      }
    );

    // Step 7: Generate RFQ automatically (mandatory step)
    state.rfqData = await controller.executeStep(
      'rfq-generation',
      'Generate RFQ from analysis data',
      async () => {
        const { RFQGenerationService } = await import('./services/rfqGenerationService.js');
        const rfqService = RFQGenerationService.getInstance();

        const rfqResult = await rfqService.generateRFQFromAnalysis({
          input: state.input,
          queryAnalysis: state.queryAnalysis,
          recommendations: state.recommendations,
          documents: state.documents,
          marketResearch: state.marketResearch
        });

        if (!rfqResult.success) {
          throw new Error(`RFQ generation failed: ${rfqResult.errors?.join(', ')}`);
        }

        return rfqResult.rfq;
      }
    );

    // Step 8: Commit and push results to GitHub (mandatory step)
    state.gitCommitInfo = await controller.executeStep(
      'git-commit-push',
      'Commit and push workflow results to GitHub',
      async () => {
        const { GitHubIntegrationService } = await import('./services/githubIntegrationService.js');
        const gitService = GitHubIntegrationService.getInstance();

        const workflowData = {
          input: state.input,
          queryAnalysis: state.queryAnalysis,
          marketResearch: state.marketResearch,
          recommendations: state.recommendations,
          documents: state.documents,
          actionPlan: state.actionPlan,
          rfqData: state.rfqData,
          timestamp: new Date().toISOString()
        };

        const commitResult = await gitService.commitAndPushResults(
          workflowData,
          `Automated procurement workflow: ${state.input.substring(0, 50)}...`
        );

        if (!commitResult.success) {
          console.warn('⚠️ Git operations failed but continuing workflow:', commitResult.error);
          // Don't throw error - workflow should complete even if git fails
        }

        return commitResult;
      }
    );

    // Mark workflow as completed
    state.status = 'completed';
    state.endTime = Date.now();

    // Get final workflow status
    const workflowStatus = controller.getStatus();

    // Log workflow completion
    logger.completeWorkflow(workflowId, 'completed', {
      totalSteps: workflowStatus.totalSteps,
      completedSteps: workflowStatus.completedSteps,
      duration: workflowStatus.totalDuration
    });

    // Complete workflow persistence
    if (isDatabaseAvailable) {
      const workflowData = {
        input: state.input,
        queryAnalysis: state.queryAnalysis,
        marketResearch: state.marketResearch,
        recommendations: state.recommendations,
        documents: state.documents,
        actionPlan: state.actionPlan,
        rfqData: state.rfqData,
        gitCommitInfo: state.gitCommitInfo,
        timestamp: new Date().toISOString()
      };

      await persistence.completeWorkflow(
        workflowId,
        'completed',
        workflowData,
        workflowStatus,
        state.endTime - state.startTime
      );

      // Auto-tag the workflow
      await persistence.autoTagWorkflow(workflowId, workflowData);
    }

    // Compile final response with all results
    const finalResponse = {
      status: "success",
      workflow_id: workflowId,
      workflow_status: workflowStatus,
      query_analysis: state.queryAnalysis,
      market_intelligence: state.marketResearch,
      recommendations: state.recommendations,
      documents: state.documents,
      action_plan: state.actionPlan,
      rfq_data: state.rfqData,
      git_commit_info: state.gitCommitInfo,
      timestamp: new Date().toISOString(),
      region: "Mangaluru, India (with global considerations)",
      execution_time_ms: state.endTime - state.startTime,
      system_status: logger.getSystemStatus()
    };

    logger.info('🎉 Procurement workflow completed successfully', {
      duration: state.endTime - state.startTime,
      steps: `${workflowStatus.completedSteps}/${workflowStatus.totalSteps}`
    }, workflowId);

    return finalResponse;

  } catch (err) {
    const error = err instanceof Error ? err : new Error(String(err));
    const errorMessage = error.message;

    state.errors.push(errorMessage);
    state.status = 'failed';
    state.endTime = Date.now();

    // Log the error
    logger.error("❌ Procurement workflow error", error, {
      input: input.substring(0, 100),
      duration: state.endTime - state.startTime
    }, workflowId);

    // Terminate workflow controller
    controller.terminate(`Workflow failed: ${errorMessage}`);

    // Get final workflow status for debugging
    const workflowStatus = controller.getStatus();

    // Log workflow failure
    logger.completeWorkflow(workflowId, 'failed', {
      error: errorMessage,
      totalSteps: workflowStatus.totalSteps,
      completedSteps: workflowStatus.completedSteps,
      failedSteps: workflowStatus.failedSteps
    });

    // Save partial results to persistence if database is available
    if (isDatabaseAvailable) {
      const workflowData = {
        input: state.input,
        queryAnalysis: state.queryAnalysis,
        marketResearch: state.marketResearch,
        recommendations: state.recommendations,
        documents: state.documents,
        actionPlan: state.actionPlan,
        error: errorMessage,
        timestamp: new Date().toISOString()
      };

      await persistence.savePartialResults(workflowId, workflowData, errorMessage);

      // Auto-tag the failed workflow
      await persistence.autoTagWorkflow(workflowId, workflowData);
    }

    // Try to save partial results to GitHub if possible
    let partialGitCommit: any = null;
    try {
      if (state.queryAnalysis || state.recommendations) {
        logger.info('🔄 Attempting to save partial results', undefined, workflowId);
        const { GitHubIntegrationService } = await import('./services/githubIntegrationService.js');
        const gitService = GitHubIntegrationService.getInstance();

        const partialData = {
          input: state.input,
          queryAnalysis: state.queryAnalysis,
          marketResearch: state.marketResearch,
          recommendations: state.recommendations,
          documents: state.documents,
          actionPlan: state.actionPlan,
          error: errorMessage,
          timestamp: new Date().toISOString()
        };

        partialGitCommit = await gitService.commitAndPushResults(
          partialData,
          `Partial procurement workflow results (failed): ${errorMessage}`
        );

        if (partialGitCommit && partialGitCommit.success) {
          logger.info('✅ Partial results saved to GitHub', { commitSha: partialGitCommit.commitSha }, workflowId);
        }
      }
    } catch (gitError) {
      logger.warn('⚠️ Failed to save partial results', gitError instanceof Error ? gitError : undefined, undefined, workflowId);
    }

    // Return comprehensive error response with partial results
    return {
      status: "error",
      workflow_id: workflowId,
      workflow_status: workflowStatus,
      message: "Procurement workflow failed but partial analysis available",
      error: errorMessage,
      partial_results: {
        query_analysis: state.queryAnalysis,
        market_intelligence: state.marketResearch,
        recommendations: state.recommendations,
        documents: state.documents,
        action_plan: state.actionPlan
      },
      git_commit_info: partialGitCommit,
      system_status: logger.getSystemStatus(),
      fallback_response: `I've partially analyzed your procurement request: "${input}".

      Based on the analysis completed before the error:
      1. Verify supplier credentials and ESG compliance
      2. Compare at least 3 quotes for purchases over $5,000
      3. Consider local Mangaluru suppliers for regional advantages
      4. Ensure all documentation meets compliance requirements
      5. Follow standard approval workflows

      Error encountered: ${errorMessage}
      Workflow ID: ${workflowId}
      Please check your API keys and system configuration.`,
      errors: state.errors,
      timestamp: new Date().toISOString(),
      execution_time_ms: state.endTime - state.startTime
    };
  }
}

interface EventInput {
  json(): Promise<{ input: string }>;
}

async function main(event: EventInput, env: any): Promise<any> {
  const startTime = Date.now();
  const mainId = `main-${Date.now()}`;

  logger.info('🚀 Starting procurement agent main function', undefined, mainId);

  try {
    const { input } = await event.json();

    if (!input || typeof input !== 'string') {
      throw new Error('Invalid input: input must be a non-empty string');
    }

    logger.info('📝 Processing input', {
      inputLength: input.length,
      inputPreview: input.substring(0, 100) + (input.length > 100 ? '...' : '')
    }, mainId);

    const result = await procurementAgentWorkflow({ input, env });

    const duration = Date.now() - startTime;
    logger.info('✅ Main function completed successfully', { duration }, mainId);

    // Add execution metadata
    return {
      ...result,
      main_execution_time_ms: duration,
      main_status: 'success',
      main_id: mainId
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    const err = error instanceof Error ? error : new Error(String(error));

    logger.error('❌ Main function failed', err, { duration }, mainId);

    return {
      status: "error",
      main_status: "failed",
      main_id: mainId,
      message: "Failed to process procurement request",
      error: err.message,
      main_execution_time_ms: duration,
      system_status: logger.getSystemStatus(),
      timestamp: new Date().toISOString()
    };
  }
}

export default main;

// Test function for development
export async function testProcurementAgent(input: string) {
  return await procurementAgentWorkflow({ input, env: {} });
}

// Example usage for testing
if (import.meta.url === `file://${process.argv[1]}`) {
  (async () => {
    const testInput = 'Find sustainable packaging suppliers in Mangaluru under $5,000 budget with ESG compliance';
    console.log('Testing Procurement Agent with LangGraph + Perplexity...');
    const result = await testProcurementAgent(testInput);
    console.log(JSON.stringify(result, null, 2));
  })();
}