import React, { useState, useMemo } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useRFQStore } from "@/store/rfqStore";
import { ActionButton } from "./ActionButton";
import { Award, TrendingDown, TrendingUp, AlertTriangle, Sparkles, Download } from "lucide-react";

interface ProposalAnalysis {
  bestPrice: string;
  worstPrice: string;
  averagePrice: number;
  priceRange: number;
  recommendations: string[];
  riskFactors: string[];
}

export function ProposalComparisonTable({ rfqId }: { rfqId: string }) {
  const rfq = useRFQStore(s => s.rfqs.find(r => r.id === rfqId));
  const quotes = useRFQStore(s => s.quotes.filter(q => q.rfqId === rfqId));
  const vendors = useRFQStore(s => s.vendors);
  const awardQuote = useRFQStore(s => s.awardQuote);
  const rejectQuote = useRFQStore(s => s.rejectQuote);

  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [aiAnalysis, setAiAnalysis] = useState<ProposalAnalysis | null>(null);

  if (!rfq) return null;

  // Collect item ids to ensure matrix alignment
  const itemIds = rfq.lineItems.map(li => li.id);

  // Calculate proposal statistics
  const proposalStats = useMemo(() => {
    if (quotes.length === 0) return null;

    const totals = quotes.map(q => q.total);
    const minPrice = Math.min(...totals);
    const maxPrice = Math.max(...totals);
    const avgPrice = totals.reduce((a, b) => a + b, 0) / totals.length;

    const bestQuote = quotes.find(q => q.total === minPrice);
    const worstQuote = quotes.find(q => q.total === maxPrice);

    return {
      minPrice,
      maxPrice,
      avgPrice,
      bestVendor: bestQuote ? vendors.find(v => v.id === bestQuote.vendorId)?.name : 'Unknown',
      worstVendor: worstQuote ? vendors.find(v => v.id === worstQuote.vendorId)?.name : 'Unknown',
      priceRange: maxPrice - minPrice,
      savings: maxPrice - minPrice
    };
  }, [quotes, vendors]);

  const generateAIAnalysis = async () => {
    if (quotes.length === 0) return;

    setIsAnalyzing(true);
    try {
      const proposalData = quotes.map(q => {
        const vendor = vendors.find(v => v.id === q.vendorId);
        return {
          vendor: vendor?.name || 'Unknown',
          total: q.total,
          currency: q.currency,
          deliveryDate: q.deliveryDate,
          deliveryTerms: q.deliveryTerms,
          notes: q.notes
        };
      });

      const prompt = `Analyze these RFQ proposals and provide insights:
      RFQ: ${rfq.title}
      Proposals: ${JSON.stringify(proposalData, null, 2)}

      Provide analysis on best value, risks, and recommendations.`;

      const response = await fetch('/api/agent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ input: prompt })
      });

      const result = await response.json();

      // Parse AI response into structured analysis
      const analysis: ProposalAnalysis = {
        bestPrice: proposalStats?.bestVendor || 'Unknown',
        worstPrice: proposalStats?.worstVendor || 'Unknown',
        averagePrice: proposalStats?.avgPrice || 0,
        priceRange: proposalStats?.priceRange || 0,
        recommendations: [
          result.recommendations || 'AI analysis completed',
          'Consider delivery terms and vendor reliability',
          'Verify compliance requirements'
        ],
        riskFactors: [
          'Price variations detected',
          'Delivery timeline differences',
          'Vendor capability assessment needed'
        ]
      };

      setAiAnalysis(analysis);
    } catch (error) {
      console.error('AI analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleAward = (quoteId: string) => {
    awardQuote(quoteId);
  };

  const handleReject = (quoteId: string) => {
    rejectQuote(quoteId);
  };

  const exportComparison = () => {
    // Generate CSV or PDF export
    const csvContent = [
      ['Vendor', 'Total', 'Currency', 'Delivery', 'Status'],
      ...quotes.map(q => {
        const vendor = vendors.find(v => v.id === q.vendorId);
        return [
          vendor?.name || 'Unknown',
          q.total.toString(),
          q.currency,
          q.deliveryDate || q.deliveryTerms || '-',
          q.status || 'Pending'
        ];
      })
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `rfq-${rfq.refNumber}-comparison.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Analysis Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              Proposal Comparison & Analysis
              {quotes.length > 0 && (
                <Badge variant="secondary">{quotes.length} Proposals</Badge>
              )}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={generateAIAnalysis}
                disabled={isAnalyzing || quotes.length === 0}
              >
                <Sparkles className="w-4 h-4 mr-2" />
                {isAnalyzing ? 'Analyzing...' : 'AI Analysis'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={exportComparison}
                disabled={quotes.length === 0}
              >
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>

        {proposalStats && (
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="flex items-center gap-2">
                <TrendingDown className="w-4 h-4 text-green-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Best Price</p>
                  <p className="font-semibold">${proposalStats.minPrice.toLocaleString()}</p>
                  <p className="text-xs text-muted-foreground">{proposalStats.bestVendor}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-red-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Highest Price</p>
                  <p className="font-semibold">${proposalStats.maxPrice.toLocaleString()}</p>
                  <p className="text-xs text-muted-foreground">{proposalStats.worstVendor}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Award className="w-4 h-4 text-blue-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Average Price</p>
                  <p className="font-semibold">${proposalStats.avgPrice.toLocaleString()}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-4 h-4 text-orange-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Potential Savings</p>
                  <p className="font-semibold text-green-600">${proposalStats.savings.toLocaleString()}</p>
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* AI Analysis Results */}
      {aiAnalysis && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="w-5 h-5" />
              AI Analysis Results
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-sm mb-2">Recommendations</h4>
                <ul className="space-y-1">
                  {aiAnalysis.recommendations.map((rec, index) => (
                    <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                      <span className="text-green-500">•</span>
                      {rec}
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-sm mb-2">Risk Factors</h4>
                <ul className="space-y-1">
                  {aiAnalysis.riskFactors.map((risk, index) => (
                    <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                      <span className="text-orange-500">•</span>
                      {risk}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Comparison Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Vendor</TableHead>
                  {itemIds.map(id => (
                    <TableHead key={id}>{rfq.lineItems.find(li => li.id === id)?.itemName || 'Item'}</TableHead>
                  ))}
                  <TableHead>Total</TableHead>
                  <TableHead>Delivery</TableHead>
                  <TableHead>Notes</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {quotes.map(q => {
                  const awarded = q.status === 'Awarded';
                  const rejected = q.status === 'Rejected';
                  const vendor = vendors.find(v => v.id === q.vendorId);
                  const isLowest = proposalStats && q.total === proposalStats.minPrice;

                  return (
                    <TableRow key={q.id} className={
                      awarded ? 'bg-green-50/50' :
                      rejected ? 'bg-red-50/50' :
                      isLowest ? 'bg-blue-50/50' : ''
                    }>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{vendor?.name || q.vendorId}</span>
                            {isLowest && <Badge variant="secondary" className="text-xs">Lowest</Badge>}
                            {awarded && <Badge className="text-xs">AWARDED</Badge>}
                            {rejected && <Badge variant="destructive" className="text-xs">REJECTED</Badge>}
                          </div>
                          <div className="flex gap-2 text-xs">
                            {vendor?.email && (
                              <a className="text-primary hover:underline" href={`mailto:${vendor.email}`} onClick={(e)=>e.stopPropagation()}>
                                Email
                              </a>
                            )}
                            {vendor?.phone && (
                              <a className="text-primary hover:underline" href={`tel:${vendor.phone}`} onClick={(e)=>e.stopPropagation()}>
                                Call
                              </a>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      {itemIds.map(id => {
                        const item = q.items.find(i => i.lineItemId === id);
                        return (
                          <TableCell key={id}>
                            {item ? (
                              <div className="text-sm">
                                <div className="font-medium">{item.unitPrice} {item.currency}</div>
                                {item.notes && <div className="text-xs text-muted-foreground truncate">{item.notes}</div>}
                              </div>
                            ) : '-'}
                          </TableCell>
                        );
                      })}
                      <TableCell>
                        <div className="font-semibold">{q.total} {q.currency}</div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {q.deliveryDate && <div>{q.deliveryDate.slice(0,10)}</div>}
                          {q.deliveryTerms && <div className="text-xs text-muted-foreground">{q.deliveryTerms}</div>}
                        </div>
                      </TableCell>
                      <TableCell className="max-w-[200px]">
                        <div className="text-sm truncate" title={q.notes}>
                          {q.notes || '-'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          {!awarded && !rejected && (
                            <>
                              <ActionButton
                                onClick={() => handleAward(q.id)}
                                variant="default"
                                size="sm"
                              >
                                Award
                              </ActionButton>
                              <ActionButton
                                onClick={() => handleReject(q.id)}
                                variant="destructive"
                                size="sm"
                              >
                                Reject
                              </ActionButton>
                            </>
                          )}
                          {awarded && (
                            <Badge className="text-xs">Awarded</Badge>
                          )}
                          {rejected && (
                            <Badge variant="destructive" className="text-xs">Rejected</Badge>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
                {quotes.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={4 + itemIds.length} className="text-center text-sm text-muted-foreground py-8">
                      No proposals received yet. Vendors will submit their quotes here.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

