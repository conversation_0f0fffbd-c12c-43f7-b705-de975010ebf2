import { Hono } from 'hono';
import { z } from 'zod';
import { AIOutputPayloadSchema } from './domain';

const app = new Hono();

function ok<T>(data: T, status = 200) {
  return new Response(JSON.stringify(data), { status, headers: { 'Content-Type': 'application/json' } });
}

// Best-effort coercion of unstructured agent output into our structured schema
// This is intentionally conservative and non-destructive; if we cannot produce a valid structure, return the original
app.post('/api/transform/agent-output', async (c) => {
  const body = await c.req.json();
  // If it's already structured according to our schema, accept it
  const structured = AIOutputPayloadSchema.safeParse(body);
  if (structured.success) return ok(structured.data);

  // Otherwise, try to detect a likely RFQ-like object with line items and vendor details in plain JSON
  try {
    // If body contains a string that looks like JSON, try to parse
    if (typeof body === 'string') {
      try { const parsed = JSON.parse(body); return ok(parsed); } catch {}
    }

    // If body has fields like rfq/report/proposal but wrong shapes, pass through
    if (body && typeof body === 'object' && (body.rfq || body.report || body.proposal)) {
      return ok({
        type: body.type || (body.rfq ? 'rfq' : body.report ? 'report' : 'proposal'),
        rfq: body.rfq, report: body.report, proposal: body.proposal, context: body.context,
      });
    }

    // Otherwise, return the original with a flag
    return ok({ type: 'raw', data: body });
  } catch (e: any) {
    return ok({ type: 'raw', data: body, error: e?.message });
  }
});

export { app as transformRoutes };

