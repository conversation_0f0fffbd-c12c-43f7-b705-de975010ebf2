import type { Vendor } from "@/store/rfqStore";

export interface VendorContactInfo {
  name: string;
  company?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  website?: string;
  address?: string;
  confidence: number; // 0-1 score indicating reliability of the data
  source: string; // Where the information was found
}

export interface VendorDiscoveryResult {
  vendors: VendorContactInfo[];
  searchQuery: string;
  timestamp: string;
}

export class VendorDiscoveryService {
  private static instance: VendorDiscoveryService;
  private cache: Map<string, VendorDiscoveryResult> = new Map();
  private readonly CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours

  static getInstance(): VendorDiscoveryService {
    if (!VendorDiscoveryService.instance) {
      VendorDiscoveryService.instance = new VendorDiscoveryService();
    }
    return VendorDiscoveryService.instance;
  }

  /**
   * Extract vendor names from AI-generated text
   */
  extractVendorNames(text: string): string[] {
    const vendorPatterns = [
      // Pattern: "To: Vendor Name"
      /To:\s*([A-Z][a-zA-Z\s&.-]+(?:Ltd|Pvt|Inc|Corp|Company|Co|Industries|Enterprises|Solutions|Systems|Technologies|Tech|Electric|Electronics|Electricals)?\.?)/gi,
      // Pattern: "Vendor: Company Name"
      /Vendor:\s*([A-Z][a-zA-Z\s&.-]+(?:Ltd|Pvt|Inc|Corp|Company|Co|Industries|Enterprises|Solutions|Systems|Technologies|Tech|Electric|Electronics|Electricals)?\.?)/gi,
      // Pattern: Company names with common suffixes
      /([A-Z][a-zA-Z\s&.-]+(?:Ltd|Pvt|Inc|Corp|Company|Co|Industries|Enterprises|Solutions|Systems|Technologies|Tech|Electric|Electronics|Electricals)\.?)/g,
      // Pattern: "Supplier: Company Name"
      /Supplier:\s*([A-Z][a-zA-Z\s&.-]+)/gi,
    ];

    const vendors = new Set<string>();
    
    for (const pattern of vendorPatterns) {
      const matches = text.matchAll(pattern);
      for (const match of matches) {
        if (match[1]) {
          const vendorName = match[1].trim();
          // Filter out common false positives
          if (this.isValidVendorName(vendorName)) {
            vendors.add(vendorName);
          }
        }
      }
    }

    return Array.from(vendors);
  }

  /**
   * Validate if extracted text is likely a vendor name
   */
  private isValidVendorName(name: string): boolean {
    const invalidPatterns = [
      /^(the|and|or|of|in|at|to|for|with|by|from)$/i,
      /^(january|february|march|april|may|june|july|august|september|october|november|december)$/i,
      /^(monday|tuesday|wednesday|thursday|friday|saturday|sunday)$/i,
      /^\d+$/,
      /^[a-z]+$/,  // All lowercase (likely not a company name)
    ];

    if (name.length < 3 || name.length > 100) return false;
    
    for (const pattern of invalidPatterns) {
      if (pattern.test(name)) return false;
    }

    return true;
  }

  /**
   * Search for vendor contact information using web search
   */
  async discoverVendorContacts(vendorNames: string[], location?: string): Promise<VendorDiscoveryResult> {
    const searchQuery = vendorNames.join(', ') + (location ? ` ${location}` : '');
    
    // Check cache first
    const cacheKey = `${searchQuery}_${location || 'global'}`;
    const cached = this.cache.get(cacheKey);
    if (cached && Date.now() - new Date(cached.timestamp).getTime() < this.CACHE_DURATION) {
      return cached;
    }

    const discoveredVendors: VendorContactInfo[] = [];

    for (const vendorName of vendorNames) {
      try {
        const vendorInfo = await this.searchVendorInfo(vendorName, location);
        if (vendorInfo) {
          discoveredVendors.push(vendorInfo);
        }
      } catch (error) {
        console.warn(`Failed to discover info for vendor: ${vendorName}`, error);
      }
    }

    const result: VendorDiscoveryResult = {
      vendors: discoveredVendors,
      searchQuery,
      timestamp: new Date().toISOString(),
    };

    // Cache the result
    this.cache.set(cacheKey, result);

    return result;
  }

  /**
   * Search for individual vendor information
   */
  private async searchVendorInfo(vendorName: string, location?: string): Promise<VendorContactInfo | null> {
    try {
      // Construct search query
      const searchTerms = [
        vendorName,
        'contact',
        'email',
        'phone',
        location || 'India'
      ].filter(Boolean).join(' ');

      // Use the existing agent API to perform web search
      const response = await fetch('/api/agent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          input: `Find contact information for vendor "${vendorName}" including email address, phone number, and company details. Focus on suppliers in ${location || 'India, particularly Mangaluru region'}.`
        })
      });

      if (!response.ok) {
        throw new Error(`Search failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Parse the AI response to extract contact information
      return this.parseContactInfo(vendorName, data);
      
    } catch (error) {
      console.error(`Error searching for vendor ${vendorName}:`, error);
      return null;
    }
  }

  /**
   * Parse AI response to extract structured contact information
   */
  private parseContactInfo(vendorName: string, aiResponse: any): VendorContactInfo | null {
    try {
      const text = typeof aiResponse === 'string' ? aiResponse : 
                   aiResponse.recommendations || aiResponse.output || JSON.stringify(aiResponse);

      // Extract email addresses
      const emailPattern = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
      const emails = text.match(emailPattern) || [];

      // Extract phone numbers (Indian format)
      const phonePattern = /(\+91[-\s]?)?(\d{10}|\d{3}[-\s]?\d{3}[-\s]?\d{4})/g;
      const phones = text.match(phonePattern) || [];

      // Extract website URLs
      const websitePattern = /(https?:\/\/[^\s]+|www\.[^\s]+)/g;
      const websites = text.match(websitePattern) || [];

      // Extract company information
      const companyPattern = new RegExp(`(${vendorName}[^.]*(?:Ltd|Pvt|Inc|Corp|Company|Co|Industries|Enterprises|Solutions|Systems|Technologies|Tech|Electric|Electronics|Electricals)[^.]*)`, 'i');
      const companyMatch = text.match(companyPattern);

      if (emails.length === 0 && phones.length === 0) {
        return null; // No useful contact info found
      }

      return {
        name: vendorName,
        company: companyMatch ? companyMatch[0].trim() : vendorName,
        email: emails[0] || undefined,
        phone: phones[0] || undefined,
        mobile: phones[1] || undefined,
        website: websites[0] || undefined,
        confidence: this.calculateConfidence(emails.length, phones.length, websites.length),
        source: 'AI Web Search'
      };

    } catch (error) {
      console.error('Error parsing contact info:', error);
      return null;
    }
  }

  /**
   * Calculate confidence score based on available information
   */
  private calculateConfidence(emailCount: number, phoneCount: number, websiteCount: number): number {
    let score = 0;
    if (emailCount > 0) score += 0.4;
    if (phoneCount > 0) score += 0.3;
    if (websiteCount > 0) score += 0.2;
    if (emailCount > 0 && phoneCount > 0) score += 0.1; // Bonus for multiple contact methods
    
    return Math.min(score, 1.0);
  }

  /**
   * Convert discovered vendor info to Vendor object for storage
   */
  convertToVendor(vendorInfo: VendorContactInfo): Vendor {
    return {
      id: `discovered_${Date.now()}_${Math.random().toString(36).slice(2, 8)}`,
      name: vendorInfo.name,
      company: vendorInfo.company,
      email: vendorInfo.email,
      phone: vendorInfo.phone,
      mobile: vendorInfo.mobile,
      website: vendorInfo.website,
      address: vendorInfo.address,
      notes: `Auto-discovered via ${vendorInfo.source} (Confidence: ${Math.round(vendorInfo.confidence * 100)}%)`,
      pendingVerification: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  /**
   * Clear cache (useful for testing or manual refresh)
   */
  clearCache(): void {
    this.cache.clear();
  }
}
