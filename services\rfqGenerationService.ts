import { z } from 'zod';

// RFQ Data Schemas
const RFQLineItemSchema = z.object({
  id: z.string(),
  itemName: z.string().min(1, 'Item name is required'),
  partNumber: z.string().optional(),
  quantity: z.number().min(1, 'Quantity must be at least 1'),
  unit: z.string().default('each'),
  materialSpec: z.string().optional(),
  notes: z.string().optional(),
});

const RFQClientSchema = z.object({
  company: z.string().min(1, 'Company name is required'),
  contactName: z.string().min(1, 'Contact name is required'),
  email: z.string().email('Valid email is required'),
  phone: z.string().optional(),
  address: z.string().optional(),
});

const RFQDataSchema = z.object({
  id: z.string(),
  refNumber: z.string().min(1, 'Reference number is required'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  client: RFQClientSchema,
  dueDate: z.string().min(1, 'Due date is required'),
  responseDeadline: z.string().min(1, 'Response deadline is required'),
  notes: z.string().optional(),
  attachments: z.array(z.string()).default([]),
  lineItems: z.array(RFQLineItemSchema).min(1, 'At least one line item is required'),
  invitedVendorIds: z.array(z.string()).default([]),
  status: z.enum(['Draft', 'Sent', 'Closed']).default('Draft'),
  createdAt: z.string(),
  history: z.array(z.any()).default([]),
});

export type RFQData = z.infer<typeof RFQDataSchema>;
export type RFQLineItem = z.infer<typeof RFQLineItemSchema>;
export type RFQClient = z.infer<typeof RFQClientSchema>;

interface RFQGenerationResult {
  success: boolean;
  rfq?: RFQData;
  errors?: string[];
  validationErrors?: z.ZodError;
}

export class RFQGenerationService {
  private static instance: RFQGenerationService;

  static getInstance(): RFQGenerationService {
    if (!RFQGenerationService.instance) {
      RFQGenerationService.instance = new RFQGenerationService();
    }
    return RFQGenerationService.instance;
  }

  /**
   * Generate RFQ from procurement analysis data
   */
  async generateRFQFromAnalysis(
    analysisData: {
      input: string;
      queryAnalysis?: string;
      recommendations?: string;
      documents?: string;
      marketResearch?: string;
    }
  ): Promise<RFQGenerationResult> {
    try {
      console.log('🔄 Generating RFQ from procurement analysis...');

      // Extract RFQ data from analysis
      const extractedData = this.extractRFQDataFromAnalysis(analysisData);
      
      // Validate the extracted data
      const validationResult = RFQDataSchema.safeParse(extractedData);
      
      if (!validationResult.success) {
        console.error('❌ RFQ validation failed:', validationResult.error);
        return {
          success: false,
          validationErrors: validationResult.error,
          errors: validationResult.error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
        };
      }

      const rfq = validationResult.data;
      
      // Save RFQ to storage (integrate with existing RFQ store)
      await this.saveRFQ(rfq);
      
      console.log('✅ RFQ generated successfully:', rfq.refNumber);
      
      return {
        success: true,
        rfq
      };

    } catch (error) {
      console.error('❌ RFQ generation failed:', error);
      return {
        success: false,
        errors: [error instanceof Error ? error.message : String(error)]
      };
    }
  }

  /**
   * Extract RFQ data from procurement analysis
   */
  private extractRFQDataFromAnalysis(analysisData: any): Partial<RFQData> {
    const timestamp = new Date().toISOString();
    const refNumber = `RFQ-${Date.now()}`;
    
    // Extract title from input or analysis
    const title = this.extractTitle(analysisData.input, analysisData.queryAnalysis);
    
    // Extract description from recommendations or documents
    const description = this.extractDescription(
      analysisData.recommendations, 
      analysisData.documents, 
      analysisData.input
    );
    
    // Extract line items from documents or analysis
    const lineItems = this.extractLineItems(analysisData.documents, analysisData.recommendations);
    
    // Set default client information (can be enhanced with actual client data)
    const client: RFQClient = {
      company: 'Procurement Agent System',
      contactName: 'Procurement Manager',
      email: '<EMAIL>',
      phone: '+91-824-XXXXXXX',
      address: 'Mangaluru, Karnataka, India'
    };

    // Set deadlines (default to 2 weeks from now)
    const dueDate = new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString();
    const responseDeadline = new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString();

    return {
      id: crypto.randomUUID(),
      refNumber,
      title,
      description,
      client,
      dueDate,
      responseDeadline,
      notes: this.extractNotes(analysisData.recommendations),
      attachments: [],
      lineItems,
      invitedVendorIds: [],
      status: 'Draft' as const,
      createdAt: timestamp,
      history: [{
        action: 'Created',
        timestamp,
        details: 'RFQ automatically generated from procurement analysis',
        user: 'Procurement Agent'
      }]
    };
  }

  private extractTitle(input: string, queryAnalysis?: string): string {
    // Try to extract a meaningful title from the input or analysis
    if (queryAnalysis && queryAnalysis.includes('procurement request')) {
      const match = queryAnalysis.match(/procurement request[:\s]+([^.]+)/i);
      if (match) return match[1].trim();
    }
    
    // Fallback to input (truncated)
    const cleanInput = input.replace(/[^\w\s]/g, '').trim();
    return cleanInput.length > 50 ? cleanInput.substring(0, 50) + '...' : cleanInput;
  }

  private extractDescription(recommendations?: string, documents?: string, input?: string): string {
    if (documents && documents.length > 100) {
      return documents.substring(0, 500) + '...';
    }
    
    if (recommendations && recommendations.length > 100) {
      return recommendations.substring(0, 500) + '...';
    }
    
    return input || 'Procurement request generated by AI agent';
  }

  private extractLineItems(documents?: string, recommendations?: string): RFQLineItem[] {
    const items: RFQLineItem[] = [];
    
    // Try to extract items from documents or recommendations
    const text = documents || recommendations || '';
    
    // Simple pattern matching for common item formats
    const itemPatterns = [
      /(\d+)\s*x?\s*([^,\n]+)/gi,
      /item[:\s]*([^,\n]+)/gi,
      /product[:\s]*([^,\n]+)/gi,
      /material[:\s]*([^,\n]+)/gi
    ];

    for (const pattern of itemPatterns) {
      const matches = text.matchAll(pattern);
      for (const match of matches) {
        if (items.length >= 5) break; // Limit to 5 items
        
        const quantity = match[1] ? parseInt(match[1]) : 1;
        const itemName = (match[2] || match[1]).trim();
        
        if (itemName && itemName.length > 3) {
          items.push({
            id: crypto.randomUUID(),
            itemName: itemName.substring(0, 100),
            partNumber: '',
            quantity: isNaN(quantity) ? 1 : quantity,
            unit: 'each',
            materialSpec: '',
            notes: ''
          });
        }
      }
      if (items.length > 0) break;
    }

    // If no items found, create a default item
    if (items.length === 0) {
      items.push({
        id: crypto.randomUUID(),
        itemName: 'Procurement Item (Please specify)',
        partNumber: '',
        quantity: 1,
        unit: 'each',
        materialSpec: '',
        notes: 'Generated from procurement analysis - please update with specific requirements'
      });
    }

    return items;
  }

  private extractNotes(recommendations?: string): string {
    if (!recommendations) return '';
    
    // Extract key points for notes
    const lines = recommendations.split('\n').filter(line => 
      line.includes('compliance') || 
      line.includes('requirement') || 
      line.includes('specification') ||
      line.includes('deadline') ||
      line.includes('budget')
    );
    
    return lines.slice(0, 5).join('\n');
  }

  /**
   * Save RFQ to storage system
   */
  private async saveRFQ(rfq: RFQData): Promise<void> {
    try {
      // Try to save via API endpoint
      const response = await fetch('/api/rfqs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(rfq)
      });

      if (!response.ok) {
        throw new Error(`Failed to save RFQ: ${response.statusText}`);
      }

      console.log('✅ RFQ saved to storage system');
    } catch (error) {
      console.warn('⚠️ Failed to save RFQ to storage, continuing...', error);
      // Don't throw error - RFQ generation should continue even if storage fails
    }
  }

  /**
   * Validate RFQ data
   */
  validateRFQ(data: any): { isValid: boolean; errors: string[] } {
    const result = RFQDataSchema.safeParse(data);
    
    if (result.success) {
      return { isValid: true, errors: [] };
    }
    
    return {
      isValid: false,
      errors: result.error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
    };
  }
}
