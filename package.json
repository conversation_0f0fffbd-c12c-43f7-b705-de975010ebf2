{"name": "agent", "private": true, "version": "0.0.0", "type": "module", "scripts": {"agent": "npx tsx agent.ts", "test": "node tests/integration-tests.js", "test:verbose": "DEBUG=* node tests/integration-tests.js", "validate": "node validate-workflow.js", "validate:types": "npx tsx --check agent.ts && npx tsx --check services/*.ts", "start": "cd app && npm run dev", "build": "cd app && npm run build"}, "dependencies": {"dotenv": "^17.1.0", "langbase": "^1.1.63", "node-fetch": "^3.3.2", "zod": "^3.25.67", "zod-to-json-schema": "^3.24.6"}}