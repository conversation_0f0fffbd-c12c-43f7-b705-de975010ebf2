import { Hono } from 'hono';
import { z } from 'zod';
import { readState, updateState } from './storage';
import { RFQSchema, VendorSchema, VendorQuoteSchema, GeneratedReportSchema } from './domain';

const app = new Hono();

function withTraceHeaders(headers: Record<string,string> = {}) {
  const requestId = Math.random().toString(36).slice(2,10);
  return { ...headers, 'X-Request-Id': requestId };
}

function ok<T>(data: T, status = 200, headers: Record<string,string> = {}) {
  return new Response(JSON.stringify(data), { status, headers: { 'Content-Type': 'application/json', ...withTraceHeaders(headers) } });
}
function bad(message: string, status = 400) {
  return ok({ error: message }, status);
}

// RFQs
app.get('/api/rfqs', async () => {
  const s = await readState();
  return ok(s.rfqs);
});

app.post('/api/rfqs', async (c) => {
  const body = await c.req.json();
  const parsed = RFQSchema.safeParse(body);
  if (!parsed.success) return bad(parsed.error.message);
  const rfq = parsed.data;
  const next = await updateState(s => ({ ...s, rfqs: [rfq, ...s.rfqs] }));
  return ok(rfq, 201);
});

app.patch('/api/rfqs/:id', async (c) => {
  const id = c.req.param('id');
  const patch = await c.req.json();
  const next = await updateState(s => ({
    ...s,
    rfqs: s.rfqs.map(r => (r.id === id ? { ...r, ...patch } : r)),
  }));
  const rfq = next.rfqs.find(r => r.id === id);
  return ok(rfq);
});

// Vendors
app.get('/api/vendors', async () => {
  const s = await readState();
  return ok(s.vendors);
});

// Vendor search endpoint
app.get('/api/vendors/search', async (c) => {
  const query = c.req.query('q') || '';
  const limit = parseInt(c.req.query('limit') || '50');
  const offset = parseInt(c.req.query('offset') || '0');

  const s = await readState();

  if (!query.trim()) {
    // Return all vendors if no query
    const paginatedVendors = s.vendors.slice(offset, offset + limit);
    return ok({
      vendors: paginatedVendors,
      total: s.vendors.length,
      hasMore: offset + limit < s.vendors.length
    });
  }

  const searchTerm = query.toLowerCase().trim();

  // Search across multiple fields
  const filteredVendors = s.vendors.filter(vendor => {
    const searchableFields = [
      vendor.name,
      vendor.email,
      vendor.phone,
      vendor.mobile,
      vendor.company,
      vendor.address,
      vendor.website,
      vendor.notes
    ];

    return searchableFields.some(field =>
      field && field.toLowerCase().includes(searchTerm)
    );
  });

  const paginatedVendors = filteredVendors.slice(offset, offset + limit);

  return ok({
    vendors: paginatedVendors,
    total: filteredVendors.length,
    hasMore: offset + limit < filteredVendors.length,
    query: query
  });
});

import { etagFrom, nowIso, ulid } from './util';

app.get('/api/vendors/:id', async (c) => {
  const id = c.req.param('id');
  const s = await readState();
  const v = s.vendors.find(x => x.id === id);
  if (!v) return bad('Not found', 404);
  const etag = etagFrom(v.id, (v as any).updatedAt);
  return ok(v, 200, { 'ETag': etag });
});

app.post('/api/vendors', async (c) => {
  const body = await c.req.json();
  const parsed = VendorSchema.safeParse(body);
  if (!parsed.success) return bad(parsed.error.message);
  const incoming = parsed.data as any;
  const ifMatch = c.req.header('if-match');
  let saved: any;
  await updateState(s => {
    let id = incoming.id;
    if (!id || id.startsWith('temp_')) id = ulid();
    const idx = s.vendors.findIndex(x => x.id === id);
    const now = nowIso();
    if (idx >= 0) {
      const current = s.vendors[idx] as any;
      const currentEtag = etagFrom(current.id, current.updatedAt);
      if (ifMatch && ifMatch !== currentEtag) {
        saved = current;
        return s; // conflict, no change
      }
      if (incoming.updatedAt && current.updatedAt && incoming.updatedAt !== current.updatedAt) {
        saved = current;
        return s;
      }
      const updated = { ...current, ...incoming, id, updatedAt: now };
      const v2 = VendorSchema.parse(updated);
      const vendors = [...s.vendors];
      vendors[idx] = v2 as any;
      saved = v2;
      return { ...s, vendors };
    } else {
      const created = { id, createdAt: now, updatedAt: now, ...incoming };
      const v2 = VendorSchema.parse(created);
      saved = v2;
      return { ...s, vendors: [v2 as any, ...s.vendors] };
    }
  });
  if (saved && (saved as any).id) {
    const etag = etagFrom(saved.id, saved.updatedAt);
    return ok(saved, 201, { 'ETag': etag });
  }
  const etag = saved ? etagFrom(saved.id, saved.updatedAt) : '';
  return ok(saved || { error: 'Conflict' }, 409, etag ? { 'ETag': etag } : {});
});

app.patch('/api/vendors/:id', async (c) => {
  const id = c.req.param('id');
  const patch = await c.req.json();
  const ifMatch = c.req.header('if-match');
  let result: any;
  let found = false;
  await updateState(s => {
    const idx = s.vendors.findIndex(x => x.id === id);
    if (idx < 0) return s;
    found = true;
    const current = s.vendors[idx] as any;
    const currentEtag = etagFrom(current.id, current.updatedAt);
    if (ifMatch && ifMatch !== currentEtag) {
      result = current;
      return s;
    }
    if (patch.updatedAt && current.updatedAt && patch.updatedAt !== current.updatedAt) {
      result = current;
      return s;
    }
    const now = nowIso();
    const updated = { ...current, ...patch, id, updatedAt: now };
    const v2 = VendorSchema.parse(updated);
    const vendors = [...s.vendors];
    vendors[idx] = v2 as any;
    result = v2;
    return { ...s, vendors };
  });
  if (!found) return bad('Not found', 404);
  const etag = etagFrom(result.id, result.updatedAt);
  const isConflict = c.req.header('if-match') && etag !== c.req.header('if-match');
  if (isConflict) {
    return ok(result, 409, { 'ETag': etag });
  }
  return ok(result, 200, { 'ETag': etag });
});

// Quotes
app.get('/api/quotes', async () => {
  const s = await readState();
  return ok(s.quotes);
});

app.post('/api/quotes', async (c) => {
  const body = await c.req.json();
  const parsed = VendorQuoteSchema.safeParse(body);
  if (!parsed.success) return bad(parsed.error.message);
  const q = parsed.data;
  await updateState(s => ({ ...s, quotes: [q, ...s.quotes] }));
  return ok(q, 201);
});

// Reports
app.get('/api/reports', async () => {
  const s = await readState();
  return ok(s.reports);
});

app.post('/api/reports', async (c) => {
  const body = await c.req.json();
  const parsed = GeneratedReportSchema.safeParse(body);
  if (!parsed.success) return bad(parsed.error.message);
  const rep = parsed.data;
  await updateState(s => ({ ...s, reports: [rep, ...s.reports] }));
  return ok(rep, 201);
});

export { app as persistenceRoutes };

