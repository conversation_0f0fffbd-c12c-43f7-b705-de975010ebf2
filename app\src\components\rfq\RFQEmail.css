/* RFQ Email Components Styling */

.rfq-scroll-container {
  max-height: 400px;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  margin-bottom: 16px;
}

.send-rfq-btn {
  width: 100%;
  padding: 12px;
  background: #497cdf;
  color: #fff;
  border: none;
  border-radius: 4px;
  font-weight: bold;
  margin-top: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.send-rfq-btn:hover {
  background: #395cab;
}

.send-rfq-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Dark theme support */
.dark .rfq-scroll-container {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
}

.dark .send-rfq-btn {
  background: #497cdf;
}

.dark .send-rfq-btn:hover {
  background: #395cab;
}

/* Custom scrollbar styling */
.rfq-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.rfq-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.rfq-scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.rfq-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dark .rfq-scroll-container::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

.dark .rfq-scroll-container::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
}

.dark .rfq-scroll-container::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--foreground));
}

/* Email modal specific styles */
.email-recipient-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.email-recipient-badge button {
  padding: 2px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.email-recipient-badge button:hover {
  background: hsl(var(--destructive) / 0.2);
}

/* Animation for success state */
.email-success-animation {
  animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Loading spinner */
.email-loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rfq-scroll-container {
    max-height: 300px;
  }
  
  .send-rfq-btn {
    padding: 10px;
    font-size: 14px;
  }
}

/* Print styles for RFQ content */
@media print {
  .rfq-scroll-container {
    max-height: none;
    overflow: visible;
    border: none;
    margin: 0;
  }
  
  .send-rfq-btn {
    display: none;
  }
}
