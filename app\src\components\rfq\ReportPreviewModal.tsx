import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Download, Edit, Send, FileText, Building, Calendar, User, DollarSign, Sparkles } from "lucide-react";
import { useRFQStore } from "@/store/rfqStore";
import type { RFQ, Quote } from "@/store/rfqStore";

interface ReportPreviewModalProps {
  rfq: RFQ;
  isOpen: boolean;
  onClose: () => void;
  reportType: 'rfq' | 'comparison' | 'award';
}

export function ReportPreviewModal({ rfq, isOpen, onClose, reportType }: ReportPreviewModalProps) {
  const quotes = useRFQStore(s => s.quotes.filter(q => q.rfqId === rfq.id));
  const vendors = useRFQStore(s => s.vendors);
  const reports = useRFQStore(s => s.reports.filter(r => r.rfqId === rfq.id));
  const [isEditing, setIsEditing] = useState(false);
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const printRef = useRef<HTMLDivElement>(null);

  const report = reports[0];

  const generateAIReport = async () => {
    setIsGeneratingAI(true);
    try {
      const prompt = `Generate a professional ${reportType} report for RFQ ${rfq.refNumber}: ${rfq.title}.
      Include executive summary, detailed analysis, and recommendations.`;

      const response = await fetch('/api/agent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ input: prompt })
      });

      const result = await response.json();
      // In a real implementation, this would update the report in the store
      console.log('AI Report generated:', result);
    } catch (error) {
      console.error('AI report generation failed:', error);
    } finally {
      setIsGeneratingAI(false);
    }
  };

  const handleDownloadPDF = () => {
    if (printRef.current) {
      const printContent = printRef.current.innerHTML;
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>${reportType.toUpperCase()} Report - ${rfq.refNumber}</title>
              <style>
                body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #ccc; padding-bottom: 20px; }
                .section { margin-bottom: 30px; }
                .section-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #333; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f5f5f5; font-weight: bold; }
                .footer { margin-top: 50px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #ccc; padding-top: 20px; }
                @media print { body { margin: 0; } }
              </style>
            </head>
            <body>
              <div class="header">
                <h1>${reportType.toUpperCase()} Report</h1>
                <h2>RFQ ${rfq.refNumber} - ${rfq.title}</h2>
                <p>Procurement Solutions Inc.</p>
                <p>Generated on ${new Date().toLocaleDateString()}</p>
              </div>
              ${printContent}
              <div class="footer">Confidential Document | Generated by Procurement Agent</div>
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    }
  };

  const renderReportContent = () => {
    if (reportType === 'comparison' && quotes.length > 0) {
      const minPrice = Math.min(...quotes.map(q => q.total));
      const maxPrice = Math.max(...quotes.map(q => q.total));
      const avgPrice = quotes.reduce((sum, q) => sum + q.total, 0) / quotes.length;

      return (
        <div className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold border-b pb-2">Executive Summary</h3>
            <p className="text-sm leading-relaxed">
              Analysis of {quotes.length} proposals for RFQ {rfq.refNumber}.
              Price range: ${minPrice.toLocaleString()} - ${maxPrice.toLocaleString()}.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">${minPrice.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Lowest Bid</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">${maxPrice.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Highest Bid</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">${avgPrice.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Average Bid</div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold border-b pb-2">Detailed Comparison</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Vendor</TableHead>
                  <TableHead>Total Amount</TableHead>
                  <TableHead>Currency</TableHead>
                  <TableHead>Delivery Date</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {quotes.map((quote) => {
                  const vendor = vendors.find(v => v.id === quote.vendorId);
                  const isLowest = quote.total === minPrice;
                  return (
                    <TableRow key={quote.id} className={isLowest ? 'bg-green-50/50' : ''}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{vendor?.name || 'Unknown Vendor'}</div>
                          <div className="text-xs text-muted-foreground">{vendor?.email}</div>
                        </div>
                      </TableCell>
                      <TableCell className="font-semibold">
                        {quote.total.toLocaleString()}
                        {isLowest && <Badge variant="secondary" className="ml-2 text-xs">Lowest</Badge>}
                      </TableCell>
                      <TableCell>{quote.currency}</TableCell>
                      <TableCell>{quote.deliveryDate ? new Date(quote.deliveryDate).toLocaleDateString() : '-'}</TableCell>
                      <TableCell>
                        <Badge variant={quote.status === 'Awarded' ? 'default' : 'secondary'}>
                          {quote.status || 'Pending'}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {report ? (
          report.sections.map((s, idx) => (
            <section key={idx} className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">{s.heading}</h3>
              {s.markdown && <div className="prose prose-sm max-w-none">
                <pre className="whitespace-pre-wrap text-sm bg-muted/50 p-4 rounded-lg">{s.markdown}</pre>
              </div>}
              {s.html && <div className="prose prose-sm max-w-none" dangerouslySetInnerHTML={{ __html: s.html }} />}
            </section>
          ))
        ) : (
          <div className="text-center py-8">
            <FileText className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-sm text-muted-foreground mb-4">No AI report available yet.</p>
            <Button onClick={generateAIReport} disabled={isGeneratingAI}>
              <Sparkles className="w-4 h-4 mr-2" />
              {isGeneratingAI ? 'Generating...' : 'Generate AI Report'}
            </Button>
          </div>
        )}
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            {reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report - {rfq.refNumber}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <div ref={printRef} className="space-y-6 p-6 bg-white">
            <div className="text-center space-y-2 border-b pb-6">
              <h1 className="text-2xl font-bold">{reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report</h1>
              <h2 className="text-lg text-muted-foreground">RFQ {rfq.refNumber} - {rfq.title}</h2>
              <div className="text-sm text-muted-foreground">
                <p>Procurement Solutions Inc.</p>
                <p>Generated on {new Date().toLocaleDateString()}</p>
              </div>
            </div>
            {renderReportContent()}
          </div>
        </div>

        <DialogFooter className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(!isEditing)}
            >
              <Edit className="w-4 h-4 mr-2" />
              {isEditing ? 'Preview' : 'Edit'}
            </Button>
            {!report && (
              <Button
                variant="outline"
                size="sm"
                onClick={generateAIReport}
                disabled={isGeneratingAI}
              >
                <Sparkles className="w-4 h-4 mr-2" />
                {isGeneratingAI ? 'Generating...' : 'Generate AI Report'}
              </Button>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              variant="outline"
              onClick={() => alert('Send functionality would be implemented here')}
            >
              <Send className="w-4 h-4 mr-2" />
              Send Report
            </Button>
            <Button onClick={handleDownloadPDF}>
              <Download className="w-4 h-4 mr-2" />
              Download PDF
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

