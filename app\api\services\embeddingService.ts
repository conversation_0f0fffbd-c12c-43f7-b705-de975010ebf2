import crypto from 'crypto';

export interface EmbeddingProvider {
  name: string;
  generateEmbedding(text: string): Promise<number[]>;
  getDimensions(): number;
}

export interface EmbeddingCache {
  get(key: string): Promise<number[] | null>;
  set(key: string, embedding: number[], ttl?: number): Promise<void>;
}

// Simple in-memory cache implementation
class MemoryEmbeddingCache implements EmbeddingCache {
  private cache = new Map<string, { embedding: number[]; expires: number }>();
  private defaultTTL = 24 * 60 * 60 * 1000; // 24 hours

  async get(key: string): Promise<number[] | null> {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    if (Date.now() > entry.expires) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.embedding;
  }

  async set(key: string, embedding: number[], ttl?: number): Promise<void> {
    const expires = Date.now() + (ttl || this.defaultTTL);
    this.cache.set(key, { embedding, expires });
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

// OpenAI embedding provider
class OpenAIEmbeddingProvider implements EmbeddingProvider {
  name = 'openai';
  private apiKey: string;
  private model = 'text-embedding-ada-002';
  private dimensions = 1536;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async generateEmbedding(text: string): Promise<number[]> {
    if (!this.apiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const response = await fetch('https://api.openai.com/v1/embeddings', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.model,
        input: text,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`OpenAI API error: ${response.status} - ${error}`);
    }

    const data = await response.json();
    
    if (!data.data || !data.data[0] || !data.data[0].embedding) {
      throw new Error('Invalid response from OpenAI API');
    }

    return data.data[0].embedding;
  }

  getDimensions(): number {
    return this.dimensions;
  }
}

// Fallback embedding provider (simple hash-based)
class FallbackEmbeddingProvider implements EmbeddingProvider {
  name = 'fallback';
  private dimensions = 1536;

  async generateEmbedding(text: string): Promise<number[]> {
    // Create a deterministic embedding based on text hash
    const hash = crypto.createHash('sha256').update(text).digest();
    const embedding = new Array(this.dimensions);
    
    // Use hash bytes to generate pseudo-random but deterministic values
    for (let i = 0; i < this.dimensions; i++) {
      const byteIndex = i % hash.length;
      const value = (hash[byteIndex] - 128) / 128; // Normalize to [-1, 1]
      embedding[i] = value;
    }
    
    // Normalize the vector
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => val / magnitude);
  }

  getDimensions(): number {
    return this.dimensions;
  }
}

export class EmbeddingService {
  private static instance: EmbeddingService;
  private providers: EmbeddingProvider[] = [];
  private cache: EmbeddingCache;
  private currentProvider: EmbeddingProvider;

  private constructor() {
    this.cache = new MemoryEmbeddingCache();
    this.initializeProviders();
  }

  static getInstance(): EmbeddingService {
    if (!EmbeddingService.instance) {
      EmbeddingService.instance = new EmbeddingService();
    }
    return EmbeddingService.instance;
  }

  private initializeProviders(): void {
    // Add OpenAI provider if API key is available
    const openaiKey = process.env.OPENAI_API_KEY;
    if (openaiKey) {
      this.providers.push(new OpenAIEmbeddingProvider(openaiKey));
    }

    // Always add fallback provider
    this.providers.push(new FallbackEmbeddingProvider());

    // Set current provider to the first available one
    this.currentProvider = this.providers[0];
    
    console.log(`🧠 Embedding service initialized with ${this.providers.length} providers`);
    console.log(`🧠 Current provider: ${this.currentProvider.name}`);
  }

  private getCacheKey(text: string, provider: string): string {
    const hash = crypto.createHash('sha256').update(`${provider}:${text}`).digest('hex');
    return `embedding:${provider}:${hash}`;
  }

  async generateEmbedding(text: string, useCache = true): Promise<number[]> {
    if (!text || text.trim().length === 0) {
      throw new Error('Text cannot be empty');
    }

    const cacheKey = this.getCacheKey(text, this.currentProvider.name);

    // Try to get from cache first
    if (useCache) {
      const cached = await this.cache.get(cacheKey);
      if (cached) {
        return cached;
      }
    }

    // Try each provider in order
    for (const provider of this.providers) {
      try {
        const embedding = await provider.generateEmbedding(text);
        
        // Validate embedding
        if (!Array.isArray(embedding) || embedding.length !== provider.getDimensions()) {
          throw new Error(`Invalid embedding dimensions: expected ${provider.getDimensions()}, got ${embedding.length}`);
        }

        // Cache the result
        if (useCache) {
          await this.cache.set(cacheKey, embedding);
        }

        return embedding;
      } catch (error) {
        console.warn(`🧠 Provider ${provider.name} failed:`, error);
        
        // If this is not the last provider, continue to next
        if (provider !== this.providers[this.providers.length - 1]) {
          continue;
        }
        
        // If all providers failed, throw the last error
        throw error;
      }
    }

    throw new Error('All embedding providers failed');
  }

  async generateMultipleEmbeddings(texts: string[], useCache = true): Promise<number[][]> {
    const embeddings = await Promise.all(
      texts.map(text => this.generateEmbedding(text, useCache))
    );
    return embeddings;
  }

  // Generate embeddings for agent workflow data
  async generateAgentEmbeddings(data: {
    input?: string;
    queryAnalysis?: any;
    recommendations?: any;
    documents?: any;
    actionPlan?: any;
  }): Promise<{
    inputEmbedding?: number[];
    outputEmbedding?: number[];
    combinedEmbedding?: number[];
  }> {
    const results: {
      inputEmbedding?: number[];
      outputEmbedding?: number[];
      combinedEmbedding?: number[];
    } = {};

    try {
      // Generate input embedding
      if (data.input) {
        results.inputEmbedding = await this.generateEmbedding(data.input);
      }

      // Generate output embedding from key results
      const outputTexts: string[] = [];
      if (data.queryAnalysis) {
        outputTexts.push(typeof data.queryAnalysis === 'string' ? data.queryAnalysis : JSON.stringify(data.queryAnalysis));
      }
      if (data.recommendations) {
        outputTexts.push(typeof data.recommendations === 'string' ? data.recommendations : JSON.stringify(data.recommendations));
      }
      if (data.actionPlan) {
        outputTexts.push(typeof data.actionPlan === 'string' ? data.actionPlan : JSON.stringify(data.actionPlan));
      }

      if (outputTexts.length > 0) {
        const outputText = outputTexts.join('\n\n');
        results.outputEmbedding = await this.generateEmbedding(outputText);
      }

      // Generate combined embedding
      const combinedTexts: string[] = [];
      if (data.input) combinedTexts.push(data.input);
      combinedTexts.push(...outputTexts);

      if (combinedTexts.length > 0) {
        const combinedText = combinedTexts.join('\n\n');
        results.combinedEmbedding = await this.generateEmbedding(combinedText);
      }

    } catch (error) {
      console.error('🧠 Failed to generate agent embeddings:', error);
      // Don't throw - embeddings are optional
    }

    return results;
  }

  // Calculate cosine similarity between two embeddings
  calculateSimilarity(embedding1: number[], embedding2: number[]): number {
    if (embedding1.length !== embedding2.length) {
      throw new Error('Embeddings must have the same dimensions');
    }

    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i];
      norm1 += embedding1[i] * embedding1[i];
      norm2 += embedding2[i] * embedding2[i];
    }

    return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
  }

  getCurrentProvider(): string {
    return this.currentProvider.name;
  }

  getDimensions(): number {
    return this.currentProvider.getDimensions();
  }

  clearCache(): void {
    if (this.cache instanceof MemoryEmbeddingCache) {
      this.cache.clear();
    }
  }

  getCacheStats(): { size: number } {
    if (this.cache instanceof MemoryEmbeddingCache) {
      return { size: this.cache.size() };
    }
    return { size: 0 };
  }
}
