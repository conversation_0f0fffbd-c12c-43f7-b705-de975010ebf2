# Langbase API key
LANGBASE_API_KEY=""

# Langbase API base URL
LANGBASE_API_URL="https://api.langbase.com/aymnsh174883/perplex-procure-agent-4b33"

# Agent run mode: hosted | local
AGENT_MODE="hosted"

# Perplexity API key (used by server-side workflows)
PERPLEXITY_API_KEY=""

# Your OpenAI API key: https://platform.openai.com/api-keys
OPENAI_API_KEY=""

# Email Configuration for RFQ sending
SMTP_HOST=""
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER=""
SMTP_PASS=""
EMAIL_FROM_NAME="Procurement Assistant"
EMAIL_FROM_ADDRESS=""

# PostgreSQL Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/procure_agent"
DATABASE_HOST="localhost"
DATABASE_PORT="5432"
DATABASE_NAME="procure_agent"
DATABASE_USER="username"
DATABASE_PASSWORD="password"
DATABASE_SSL="false"
DATABASE_POOL_MIN="2"
DATABASE_POOL_MAX="10"
