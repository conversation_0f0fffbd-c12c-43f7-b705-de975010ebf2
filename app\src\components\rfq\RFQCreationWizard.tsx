import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Calendar, FileText, Users, Settings, Sparkles, Paperclip } from "lucide-react";
import { useRFQStore } from "@/store/rfqStore";
import { AdvancedFileUpload } from "./AdvancedFileUpload";
import type { RFQ, PartyInfo, RFQLineItem, Attachment } from "@/store/rfqStore";

interface WizardStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  completed: boolean;
}

interface RFQFormData {
  // Basic Info
  title: string;
  refNumber: string;
  description: string;
  
  // Client/Vendor Info
  client: PartyInfo;
  
  // Dates
  dueDate: string;
  responseDeadline: string;
  
  // Notes and Instructions
  notes: string;

  // Attachments
  attachments: Attachment[];

  // Line Items
  lineItems: Omit<RFQLineItem, 'id'>[];
}

export function RFQCreationWizard({ onClose, onComplete }: { 
  onClose: () => void; 
  onComplete: (rfq: RFQ) => void; 
}) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const createRFQ = useRFQStore(s => s.createRFQ);
  
  const [formData, setFormData] = useState<RFQFormData>({
    title: '',
    refNumber: `RFQ-${Date.now()}`,
    description: '',
    client: {
      company: '',
      contactName: '',
      email: '',
      phone: '',
      address: ''
    },
    dueDate: '',
    responseDeadline: '',
    notes: '',
    attachments: [],
    lineItems: []
  });

  const steps: WizardStep[] = [
    {
      id: 'basic',
      title: 'Basic Information',
      description: 'RFQ title, reference number, and description',
      icon: <FileText className="w-5 h-5" />,
      completed: !!(formData.title && formData.refNumber)
    },
    {
      id: 'client',
      title: 'Client Information',
      description: 'Company details and contact information',
      icon: <Users className="w-5 h-5" />,
      completed: !!(formData.client.company)
    },
    {
      id: 'dates',
      title: 'Dates & Deadlines',
      description: 'Due dates and response deadlines',
      icon: <Calendar className="w-5 h-5" />,
      completed: !!(formData.dueDate && formData.responseDeadline)
    },
    {
      id: 'attachments',
      title: 'Attachments',
      description: 'Upload drawings, specifications, and documents',
      icon: <Paperclip className="w-5 h-5" />,
      completed: true // Optional step
    },
    {
      id: 'details',
      title: 'Details & Instructions',
      description: 'Notes, instructions, and line items',
      icon: <Settings className="w-5 h-5" />,
      completed: formData.lineItems.length > 0
    }
  ];

  const updateFormData = (updates: Partial<RFQFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const generateAIContent = async (type: 'description' | 'notes' | 'lineItems') => {
    setIsGeneratingAI(true);
    try {
      const prompt = type === 'description' 
        ? `Generate a professional RFQ description for: ${formData.title}`
        : type === 'notes'
        ? `Generate detailed instructions and notes for RFQ: ${formData.title} - ${formData.description}`
        : `Generate line items for RFQ: ${formData.title} - ${formData.description}`;

      const response = await fetch('/api/agent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ input: prompt })
      });

      const result = await response.json();
      
      if (type === 'description') {
        updateFormData({ description: result.documents || result.recommendations || 'AI-generated description' });
      } else if (type === 'notes') {
        updateFormData({ notes: result.action_plan || result.recommendations || 'AI-generated notes' });
      } else if (type === 'lineItems') {
        // Parse AI response to create line items
        const aiLineItems = [
          {
            itemName: 'Sample Item 1',
            partNumber: 'P001',
            quantity: 1,
            unit: 'each',
            materialSpec: 'Standard specification',
            notes: 'AI-generated item'
          }
        ];
        updateFormData({ lineItems: aiLineItems });
      }
    } catch (error) {
      console.error('AI generation failed:', error);
    } finally {
      setIsGeneratingAI(false);
    }
  };

  const addLineItem = () => {
    const newItem: Omit<RFQLineItem, 'id'> = {
      itemName: '',
      partNumber: '',
      quantity: 1,
      unit: 'each',
      materialSpec: '',
      notes: ''
    };
    updateFormData({ lineItems: [...formData.lineItems, newItem] });
  };

  const updateLineItem = (index: number, updates: Partial<Omit<RFQLineItem, 'id'>>) => {
    const updatedItems = formData.lineItems.map((item, i) => 
      i === index ? { ...item, ...updates } : item
    );
    updateFormData({ lineItems: updatedItems });
  };

  const removeLineItem = (index: number) => {
    const updatedItems = formData.lineItems.filter((_, i) => i !== index);
    updateFormData({ lineItems: updatedItems });
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    const rfq = createRFQ({
      id: undefined as any,
      refNumber: formData.refNumber,
      title: formData.title,
      description: formData.description,
      status: "Draft" as any,
      client: formData.client,
      createdAt: new Date().toISOString(),
      dueDate: formData.dueDate,
      responseDeadline: formData.responseDeadline,
      notes: formData.notes,
      attachments: formData.attachments,
      lineItems: formData.lineItems.map(item => ({
        ...item,
        id: crypto.randomUUID()
      })) as RFQLineItem[],
      invitedVendorIds: [],
      history: [] as any,
    } as any);
    
    onComplete(rfq);
    onClose();
  };

  const renderStepContent = () => {
    switch (steps[currentStep].id) {
      case 'basic':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="title">RFQ Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => updateFormData({ title: e.target.value })}
                placeholder="Enter RFQ title"
              />
            </div>
            <div>
              <Label htmlFor="refNumber">Reference Number *</Label>
              <Input
                id="refNumber"
                value={formData.refNumber}
                onChange={(e) => updateFormData({ refNumber: e.target.value })}
                placeholder="RFQ-001"
              />
            </div>
            <div>
              <div className="flex items-center justify-between">
                <Label htmlFor="description">Description</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => generateAIContent('description')}
                  disabled={isGeneratingAI || !formData.title}
                >
                  <Sparkles className="w-4 h-4 mr-2" />
                  {isGeneratingAI ? 'Generating...' : 'AI Generate'}
                </Button>
              </div>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => updateFormData({ description: e.target.value })}
                placeholder="Describe the RFQ requirements..."
                rows={4}
              />
            </div>
          </div>
        );

      case 'client':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="company">Company Name *</Label>
              <Input
                id="company"
                value={formData.client.company}
                onChange={(e) => updateFormData({ 
                  client: { ...formData.client, company: e.target.value }
                })}
                placeholder="Company name"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="contactName">Contact Name</Label>
                <Input
                  id="contactName"
                  value={formData.client.contactName || ''}
                  onChange={(e) => updateFormData({ 
                    client: { ...formData.client, contactName: e.target.value }
                  })}
                  placeholder="Contact person"
                />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.client.email || ''}
                  onChange={(e) => updateFormData({ 
                    client: { ...formData.client, email: e.target.value }
                  })}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={formData.client.phone || ''}
                  onChange={(e) => updateFormData({ 
                    client: { ...formData.client, phone: e.target.value }
                  })}
                  placeholder="Phone number"
                />
              </div>
              <div>
                <Label htmlFor="address">Address</Label>
                <Input
                  id="address"
                  value={formData.client.address || ''}
                  onChange={(e) => updateFormData({ 
                    client: { ...formData.client, address: e.target.value }
                  })}
                  placeholder="Company address"
                />
              </div>
            </div>
          </div>
        );

      case 'dates':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="dueDate">Due Date *</Label>
              <Input
                id="dueDate"
                type="date"
                value={formData.dueDate}
                onChange={(e) => updateFormData({ dueDate: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="responseDeadline">Response Deadline *</Label>
              <Input
                id="responseDeadline"
                type="date"
                value={formData.responseDeadline}
                onChange={(e) => updateFormData({ responseDeadline: e.target.value })}
              />
            </div>
          </div>
        );

      case 'attachments':
        return (
          <div className="space-y-4">
            <AdvancedFileUpload
              attachments={formData.attachments}
              onAttachmentsChange={(attachments) => updateFormData({ attachments })}
              title="RFQ Attachments"
              description="Upload drawings, specifications, terms & conditions, and other supporting documents"
              maxFiles={20}
              maxSize={25}
            />
          </div>
        );

      case 'details':
        return (
          <div className="space-y-6">
            <div>
              <div className="flex items-center justify-between">
                <Label htmlFor="notes">Notes & Instructions</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => generateAIContent('notes')}
                  disabled={isGeneratingAI || !formData.title}
                >
                  <Sparkles className="w-4 h-4 mr-2" />
                  {isGeneratingAI ? 'Generating...' : 'AI Generate'}
                </Button>
              </div>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => updateFormData({ notes: e.target.value })}
                placeholder="Additional notes and instructions..."
                rows={3}
              />
            </div>
            
            <Separator />
            
            <div>
              <div className="flex items-center justify-between mb-4">
                <Label>Line Items</Label>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => generateAIContent('lineItems')}
                    disabled={isGeneratingAI || !formData.title}
                  >
                    <Sparkles className="w-4 h-4 mr-2" />
                    AI Generate
                  </Button>
                  <Button type="button" size="sm" onClick={addLineItem}>
                    Add Item
                  </Button>
                </div>
              </div>
              
              {formData.lineItems.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No line items added yet. Click "Add Item" or "AI Generate" to get started.
                </div>
              ) : (
                <div className="space-y-4">
                  {formData.lineItems.map((item, index) => (
                    <Card key={index} className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Label>Item Name</Label>
                          <Input
                            value={item.itemName}
                            onChange={(e) => updateLineItem(index, { itemName: e.target.value })}
                            placeholder="Item name"
                          />
                        </div>
                        <div>
                          <Label>Part Number</Label>
                          <Input
                            value={item.partNumber || ''}
                            onChange={(e) => updateLineItem(index, { partNumber: e.target.value })}
                            placeholder="Part number"
                          />
                        </div>
                        <div className="flex gap-2">
                          <div className="flex-1">
                            <Label>Quantity</Label>
                            <Input
                              type="number"
                              value={item.quantity}
                              onChange={(e) => updateLineItem(index, { quantity: parseInt(e.target.value) || 1 })}
                              min="1"
                            />
                          </div>
                          <div className="flex-1">
                            <Label>Unit</Label>
                            <Input
                              value={item.unit || ''}
                              onChange={(e) => updateLineItem(index, { unit: e.target.value })}
                              placeholder="each"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="mt-4 flex gap-4">
                        <div className="flex-1">
                          <Label>Material/Spec</Label>
                          <Input
                            value={item.materialSpec || ''}
                            onChange={(e) => updateLineItem(index, { materialSpec: e.target.value })}
                            placeholder="Material specification"
                          />
                        </div>
                        <div className="flex items-end">
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            onClick={() => removeLineItem(index)}
                          >
                            Remove
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <CardHeader>
          <CardTitle>Create New RFQ</CardTitle>
          <CardDescription>
            Follow the steps to create a comprehensive Request for Quotation
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Progress Steps */}
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  index === currentStep 
                    ? 'border-primary bg-primary text-primary-foreground' 
                    : step.completed 
                    ? 'border-green-500 bg-green-500 text-white'
                    : 'border-muted-foreground bg-background'
                }`}>
                  {step.completed ? '✓' : step.icon}
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-2 ${
                    step.completed ? 'bg-green-500' : 'bg-muted'
                  }`} />
                )}
              </div>
            ))}
          </div>

          {/* Current Step Info */}
          <div className="text-center">
            <h3 className="text-lg font-semibold">{steps[currentStep].title}</h3>
            <p className="text-muted-foreground">{steps[currentStep].description}</p>
          </div>

          <Separator />

          {/* Step Content */}
          <div className="min-h-[400px] overflow-y-auto">
            {renderStepContent()}
          </div>

          {/* Navigation */}
          <div className="flex justify-between pt-4 border-t">
            <Button
              variant="outline"
              onClick={onClose}
            >
              Cancel
            </Button>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 0}
              >
                Previous
              </Button>
              
              {currentStep === steps.length - 1 ? (
                <Button
                  onClick={handleComplete}
                  disabled={!steps.every(step => step.completed)}
                >
                  Create RFQ
                </Button>
              ) : (
                <Button
                  onClick={handleNext}
                  disabled={!steps[currentStep].completed}
                >
                  Next
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
