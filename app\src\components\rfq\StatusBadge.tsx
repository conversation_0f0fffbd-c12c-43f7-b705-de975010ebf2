import { Badge } from "@/components/ui/badge";
import type { RFQStatus } from "@/store/rfqStore";

export function StatusBadge({ status }: { status: RFQStatus }) {
  const variant =
    status === "Draft" ? "outline" :
    status === "Sent" ? "secondary" :
    status === "Responded" ? "default" :
    status === "Awarded" ? "default" :
    status === "Closed" ? "outline" : "outline";

  return <Badge variant={variant as any}>{status}</Badge>;
}

