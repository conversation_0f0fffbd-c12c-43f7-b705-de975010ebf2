import { useRFQStore } from "@/store/rfqStore";

export function DevVendorDebugPanel() {
  if (process.env.NODE_ENV !== 'development') return null;
  const vendors = useRFQStore(s => s.vendors);
  return (
    <div className="p-2 border rounded bg-muted/30 text-xs space-y-1">
      <div className="font-semibold">Vendor Debug (dev only)</div>
      {vendors.map(v => (
        <div key={v.id} className="flex gap-2">
          <span className="font-mono">{v.id}</span>
          <span>{v.name}</span>
          <span className="text-muted-foreground">updatedAt: {v.updatedAt}</span>
          {v.etag && <span className="text-muted-foreground">etag: {v.etag}</span>}
        </div>
      ))}
      {vendors.length === 0 && <div className="text-muted-foreground">No vendors</div>}
    </div>
  );
}

