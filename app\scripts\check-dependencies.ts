#!/usr/bin/env node

import { existsSync } from 'fs';
import { join } from 'path';
import { execSync } from 'child_process';

interface DependencyCheck {
  name: string;
  required: boolean;
  installed: boolean;
  version?: string;
  error?: string;
}

class DependencyChecker {
  private checks: DependencyCheck[] = [];
  private projectRoot: string;

  constructor() {
    this.projectRoot = process.cwd();
  }

  // Check if a package is installed
  private checkPackage(packageName: string, required = true): DependencyCheck {
    const check: DependencyCheck = {
      name: packageName,
      required,
      installed: false
    };

    try {
      // Check if package exists in node_modules
      const packagePath = join(this.projectRoot, 'node_modules', packageName);
      const packageJsonPath = join(packagePath, 'package.json');
      
      if (existsSync(packageJsonPath)) {
        check.installed = true;
        
        // Try to get version
        try {
          const packageJson = require(packageJsonPath);
          check.version = packageJson.version;
        } catch (error) {
          check.error = 'Could not read package version';
        }
      } else {
        check.error = 'Package not found in node_modules';
      }
    } catch (error) {
      check.error = error instanceof Error ? error.message : 'Unknown error';
    }

    return check;
  }

  // Check Node.js version
  private checkNodeVersion(): DependencyCheck {
    const check: DependencyCheck = {
      name: 'Node.js',
      required: true,
      installed: true,
      version: process.version
    };

    // Check if Node.js version is compatible (>= 18.0.0)
    const majorVersion = parseInt(process.version.slice(1).split('.')[0]);
    if (majorVersion < 18) {
      check.error = `Node.js ${process.version} is too old. Requires >= 18.0.0`;
    }

    return check;
  }

  // Check npm version
  private checkNpmVersion(): DependencyCheck {
    const check: DependencyCheck = {
      name: 'npm',
      required: true,
      installed: false
    };

    try {
      const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
      check.installed = true;
      check.version = npmVersion;
    } catch (error) {
      check.error = 'npm not found or not accessible';
    }

    return check;
  }

  // Check environment variables
  private checkEnvironmentVariables(): DependencyCheck[] {
    const requiredEnvVars = [
      'DATABASE_HOST',
      'DATABASE_NAME', 
      'DATABASE_USER',
      'DATABASE_PASSWORD'
    ];

    const optionalEnvVars = [
      'OPENAI_API_KEY',
      'PERPLEXITY_API_KEY',
      'SMTP_HOST',
      'SMTP_USER'
    ];

    const checks: DependencyCheck[] = [];

    // Check required environment variables
    requiredEnvVars.forEach(varName => {
      const check: DependencyCheck = {
        name: `ENV: ${varName}`,
        required: true,
        installed: !!process.env[varName]
      };

      if (!check.installed) {
        check.error = 'Environment variable not set';
      }

      checks.push(check);
    });

    // Check optional environment variables
    optionalEnvVars.forEach(varName => {
      const check: DependencyCheck = {
        name: `ENV: ${varName}`,
        required: false,
        installed: !!process.env[varName]
      };

      if (!check.installed) {
        check.error = 'Optional environment variable not set';
      }

      checks.push(check);
    });

    return checks;
  }

  // Run all dependency checks
  async runChecks(): Promise<boolean> {
    console.log('🔍 Checking dependencies and environment...\n');

    // System checks
    this.checks.push(this.checkNodeVersion());
    this.checks.push(this.checkNpmVersion());

    // Critical package checks
    const criticalPackages = [
      'pg',
      '@types/pg',
      'hono',
      'vite',
      'tsx',
      'typescript'
    ];

    criticalPackages.forEach(pkg => {
      this.checks.push(this.checkPackage(pkg, true));
    });

    // Optional package checks
    const optionalPackages = [
      'dotenv',
      'zod',
      'cross-env',
      'concurrently'
    ];

    optionalPackages.forEach(pkg => {
      this.checks.push(this.checkPackage(pkg, false));
    });

    // Environment variable checks
    this.checks.push(...this.checkEnvironmentVariables());

    // Display results
    this.displayResults();

    // Return overall status
    const hasErrors = this.checks.some(check => check.required && !check.installed);
    return !hasErrors;
  }

  private displayResults(): void {
    console.log('📋 Dependency Check Results:');
    console.log('============================\n');

    // Group checks by category
    const categories = {
      'System': this.checks.filter(c => ['Node.js', 'npm'].includes(c.name)),
      'Critical Packages': this.checks.filter(c => c.required && !c.name.startsWith('ENV:')),
      'Optional Packages': this.checks.filter(c => !c.required && !c.name.startsWith('ENV:')),
      'Environment Variables': this.checks.filter(c => c.name.startsWith('ENV:'))
    };

    Object.entries(categories).forEach(([category, checks]) => {
      if (checks.length === 0) return;

      console.log(`\n${category}:`);
      console.log('-'.repeat(category.length + 1));

      checks.forEach(check => {
        const status = check.installed ? '✅' : (check.required ? '❌' : '⚠️');
        const version = check.version ? ` (v${check.version})` : '';
        const error = check.error ? ` - ${check.error}` : '';
        
        console.log(`${status} ${check.name}${version}${error}`);
      });
    });

    // Summary
    const totalChecks = this.checks.length;
    const passedChecks = this.checks.filter(c => c.installed).length;
    const requiredFailed = this.checks.filter(c => c.required && !c.installed).length;
    const optionalFailed = this.checks.filter(c => !c.required && !c.installed).length;

    console.log('\n📊 Summary:');
    console.log('===========');
    console.log(`Total checks: ${totalChecks}`);
    console.log(`Passed: ${passedChecks}`);
    console.log(`Required failed: ${requiredFailed}`);
    console.log(`Optional failed: ${optionalFailed}`);

    if (requiredFailed > 0) {
      console.log('\n❌ Some required dependencies are missing!');
      console.log('\n🔧 To fix missing packages, run:');
      console.log('   npm install');
      console.log('\n🔧 To fix missing environment variables:');
      console.log('   1. Copy .env.example to .env');
      console.log('   2. Fill in the required values');
    } else {
      console.log('\n✅ All required dependencies are satisfied!');
      
      if (optionalFailed > 0) {
        console.log(`\n⚠️ ${optionalFailed} optional dependencies are missing (this is OK)`);
      }
    }
  }

  // Generate installation commands for missing packages
  generateInstallCommands(): string[] {
    const missingPackages = this.checks
      .filter(check => check.required && !check.installed && !check.name.startsWith('ENV:'))
      .map(check => check.name);

    const commands: string[] = [];

    if (missingPackages.length > 0) {
      commands.push(`npm install ${missingPackages.join(' ')}`);
    }

    return commands;
  }
}

// CLI interface
async function main() {
  const checker = new DependencyChecker();
  
  try {
    const allGood = await checker.runChecks();
    
    if (!allGood) {
      console.log('\n🚨 Dependency check failed!');
      
      const installCommands = checker.generateInstallCommands();
      if (installCommands.length > 0) {
        console.log('\n💡 Run these commands to fix missing packages:');
        installCommands.forEach(cmd => console.log(`   ${cmd}`));
      }
      
      process.exit(1);
    } else {
      console.log('\n🎉 All dependency checks passed!');
      process.exit(0);
    }
  } catch (error) {
    console.error('\n💥 Dependency check crashed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { DependencyChecker };
