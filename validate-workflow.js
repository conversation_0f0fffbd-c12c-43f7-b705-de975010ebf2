#!/usr/bin/env node

/**
 * Standalone Workflow Validation Script
 * Tests the enhanced procurement agent workflow directly
 */

import { testProcurementAgent } from './agent.js';

async function validateWorkflow() {
  console.log('🧪 Validating Enhanced Procurement Agent Workflow');
  console.log('=' .repeat(60));

  const testCases = [
    {
      name: 'Basic Procurement Request',
      input: 'I need to procure 50 office chairs for our Mangaluru office',
      timeout: 120000
    },
    {
      name: 'Complex Industrial Equipment',
      input: 'Need 3 CNC machines, 2 welding stations, safety compliance required, budget 30 lakhs, delivery in Mangaluru within 60 days',
      timeout: 180000
    },
    {
      name: 'Simple Office Supplies',
      input: 'Office supplies: 100 pens, 50 notebooks, 20 staplers for Mangaluru branch',
      timeout: 90000
    }
  ];

  let totalTests = 0;
  let passedTests = 0;
  const startTime = Date.now();

  for (const testCase of testCases) {
    totalTests++;
    console.log(`\n🧪 Test ${totalTests}: ${testCase.name}`);
    console.log('-'.repeat(40));
    
    const testStartTime = Date.now();
    
    try {
      console.log(`📤 Input: "${testCase.input.substring(0, 80)}${testCase.input.length > 80 ? '...' : ''}"`);
      
      // Create timeout promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Test timeout')), testCase.timeout);
      });

      // Run the workflow
      const workflowPromise = testProcurementAgent(testCase.input);
      
      const result = await Promise.race([workflowPromise, timeoutPromise]);
      
      const testDuration = Date.now() - testStartTime;
      
      // Validate result
      const validation = validateResult(result, testCase);
      
      if (validation.success) {
        passedTests++;
        console.log('✅ Test PASSED');
        console.log(`⏱️ Duration: ${testDuration}ms`);
        console.log(`🆔 Workflow ID: ${result.workflow_id || 'N/A'}`);
        console.log(`📊 Steps: ${result.workflow_status?.completedSteps || 0}/${result.workflow_status?.totalSteps || 0}`);
        
        if (result.rfq_data) {
          console.log(`📋 RFQ Generated: ${result.rfq_data.refNumber || 'N/A'}`);
        }
        
        if (result.git_commit_info?.success) {
          console.log(`🔗 Git Commit: ${result.git_commit_info.commitSha?.substring(0, 8) || 'N/A'}`);
        }
      } else {
        console.log('❌ Test FAILED');
        console.log(`⏱️ Duration: ${testDuration}ms`);
        console.log('🔍 Validation errors:');
        validation.errors.forEach(error => console.log(`  - ${error}`));
        
        if (result.error) {
          console.log(`💥 Workflow error: ${result.error}`);
        }
      }
      
    } catch (error) {
      const testDuration = Date.now() - testStartTime;
      console.log('❌ Test FAILED');
      console.log(`⏱️ Duration: ${testDuration}ms`);
      console.log(`💥 Error: ${error.message}`);
      
      if (error.message === 'Test timeout') {
        console.log('⏰ Test exceeded timeout limit');
      }
    }
  }

  // Generate summary
  const totalDuration = Date.now() - startTime;
  const successRate = ((passedTests / totalTests) * 100).toFixed(1);
  
  console.log('\n📊 Validation Summary');
  console.log('=' .repeat(60));
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests} ✅`);
  console.log(`Failed: ${totalTests - passedTests} ❌`);
  console.log(`Success Rate: ${successRate}%`);
  console.log(`Total Duration: ${totalDuration}ms`);
  console.log(`Average Duration: ${Math.round(totalDuration / totalTests)}ms per test`);

  // System status
  console.log('\n🔧 System Status:');
  console.log(`Node.js Version: ${process.version}`);
  console.log(`Platform: ${process.platform}`);
  console.log(`Memory Usage: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`);

  // Recommendations
  console.log('\n💡 Recommendations:');
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! The enhanced workflow is functioning correctly.');
    console.log('✨ Key improvements implemented:');
    console.log('  - Deterministic workflow termination');
    console.log('  - Automated RFQ generation');
    console.log('  - GitHub integration for version control');
    console.log('  - Comprehensive logging and monitoring');
    console.log('  - Robust error handling and recovery');
  } else {
    console.log('⚠️ Some tests failed. Please review the errors above.');
    console.log('🔍 Common issues to check:');
    console.log('  - API keys (PERPLEXITY_API_KEY, GITHUB_TOKEN)');
    console.log('  - Network connectivity');
    console.log('  - File system permissions');
    console.log('  - Git configuration');
  }

  console.log('\n🎯 Validation completed!');
  process.exit(passedTests === totalTests ? 0 : 1);
}

function validateResult(result, testCase) {
  const errors = [];
  
  // Basic structure validation
  if (!result || typeof result !== 'object') {
    errors.push('Result is not a valid object');
    return { success: false, errors };
  }

  if (!result.status) {
    errors.push('Missing status field');
  }

  if (!result.timestamp) {
    errors.push('Missing timestamp field');
  }

  // Success case validation
  if (result.status === 'success') {
    if (!result.workflow_id) {
      errors.push('Missing workflow_id for successful workflow');
    }

    if (!result.workflow_status) {
      errors.push('Missing workflow_status for successful workflow');
    } else {
      if (!result.workflow_status.isComplete) {
        errors.push('Workflow marked as success but not complete');
      }

      if (result.workflow_status.failedSteps > 0) {
        errors.push(`Workflow has ${result.workflow_status.failedSteps} failed steps`);
      }

      if (result.workflow_status.completedSteps < 6) {
        errors.push(`Expected at least 6 completed steps, got ${result.workflow_status.completedSteps}`);
      }
    }

    // Validate key components
    if (!result.query_analysis) {
      errors.push('Missing query analysis');
    }

    if (!result.recommendations) {
      errors.push('Missing recommendations');
    }

    if (!result.rfq_data) {
      errors.push('Missing RFQ data');
    } else {
      if (!result.rfq_data.id || !result.rfq_data.title) {
        errors.push('RFQ data is incomplete');
      }
    }

    if (!result.git_commit_info) {
      errors.push('Missing git commit info');
    }

    if (typeof result.execution_time_ms !== 'number' || result.execution_time_ms <= 0) {
      errors.push('Invalid execution time');
    }

  } else if (result.status === 'error') {
    // Error case validation
    if (!result.error) {
      errors.push('Error status but no error message provided');
    }

    // For error cases, we still expect some partial results
    if (!result.workflow_id) {
      errors.push('Missing workflow_id even for failed workflow');
    }

  } else {
    errors.push(`Unknown status: ${result.status}`);
  }

  return {
    success: errors.length === 0,
    errors
  };
}

// Run validation if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  validateWorkflow().catch(error => {
    console.error('❌ Validation failed:', error);
    process.exit(1);
  });
}

export { validateWorkflow, validateResult };
