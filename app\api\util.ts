import { createHash, randomBytes } from 'crypto';

// ULID implementation (time-sortable, Crockford Base32)
const ENCODING: string = '0123456789ABCDEFGHJKMNPQRSTVWXYZ';

function encodeTime(time: number, len = 10): string {
  let out = '';
  for (let i = len - 1; i >= 0; i--) {
    const mod = time % 32;
    out = ENCODING[mod] + out;
    time = (time - mod) / 32;
  }
  return out;
}

function encodeRandom(len: number): string {
  // 16 random bytes gives us ample entropy; map into base32
  const bytes = randomBytes(len);
  let out = '';
  for (let i = 0; i < bytes.length; i++) {
    out += ENCODING[bytes[i] % 32];
  }
  return out.slice(0, len);
}

export function ulid(): string {
  const time = Date.now();
  return encodeTime(time) + encodeRandom(16);
}

export function nowIso(): string {
  return new Date().toISOString();
}

export function etagFrom(id: string, updatedAt?: string): string {
  const input = `${id}|${updatedAt || ''}`;
  const digest = createHash('sha1').update(input).digest('hex');
  return `"${digest}"`;
}

