export type UUID = string;

export type Money = {
  currency: string;
  amount: number;
};

export type User = {
  id: UUID;
  name: string;
  role:
    | "Requestor"
    | "Approver"
    | "Procurement"
    | "AP Clerk"
    | "Warehouse"
    | "Finance"
    | "Administrator"
    | "Vendor";
  departmentId?: UUID;
};

export type Department = {
  id: UUID;
  name: string;
};

export type Category = {
  id: UUID;
  name: string;
};

export type Product = {
  id: UUID;
  name: string;
  categoryId?: UUID;
  uom?: string;
  preferredVendorId?: UUID;
};

export type Budget = {
  id: UUID;
  departmentId: UUID;
  amount: number;
  currency: string;
  fiscalYear: number;
  utilized: number;
};

export type PurchaseRequisitionLine = {
  id: UUID;
  productId?: UUID;
  description: string;
  quantity: number;
  unitPrice: number;
  currency: string;
  categoryId?: UUID;
};

export type PurchaseRequisition = {
  id: UUID;
  requesterId: UUID;
  departmentId?: UUID;
  justification?: string;
  status: "draft" | "submitted" | "pending_approval" | "approved" | "rejected";
  lines: PurchaseRequisitionLine[];
  attachments?: { name: string; url?: string }[];
  createdAt: string;
  updatedAt: string;
  totalAmount: number;
  currency: string;
};

export type Vendor = {
  id: UUID;
  name: string;
  email?: string;
  phone?: string;
  mobile?: string;
  company?: string;
  address?: string;
  website?: string;
  favorite?: boolean;
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
  etag?: string;
  pendingVerification?: boolean;
};

export type RequestForQuote = {
  id: UUID;
  requisitionId: UUID;
  productIds: UUID[];
  deadline: string;
  invitedVendorIds: UUID[];
};

export type VendorProposal = {
  id: UUID;
  rfqId: UUID;
  vendorId: UUID;
  price: number;
  currency: string;
  terms?: string;
  deliveryWindow?: string;
};

export type PurchaseOrderLine = {
  id: UUID;
  productId?: UUID;
  description: string;
  quantity: number;
  unitPrice: number;
  currency: string;
};

export type PurchaseOrder = {
  id: UUID;
  vendorId: UUID;
  lines: PurchaseOrderLine[];
  status: "pending" | "approved" | "sent" | "acknowledged";
  deliveryDate?: string;
  totalAmount: number;
  currency: string;
};

export type GoodsReceiptNoteLine = {
  id: UUID;
  poLineId?: UUID;
  productId?: UUID;
  quantityReceived: number;
  batch?: string;
  serial?: string;
  expiryDate?: string;
};

export type GoodsReceiptNote = {
  id: UUID;
  poId: UUID;
  lines: GoodsReceiptNoteLine[];
};

export type InvoiceLine = {
  id: UUID;
  description: string;
  amount: number;
  currency: string;
};

export type Invoice = {
  id: UUID;
  vendorId: UUID;
  poId?: UUID;
  grnId?: UUID;
  lines: InvoiceLine[];
  status: "draft" | "submitted" | "matched" | "exception" | "approved" | "rejected";
  totalAmount: number;
  currency: string;
};

export type PaymentTransaction = {
  id: UUID;
  invoiceId: UUID;
  method: "NEFT" | "RTGS" | "UPI" | "ACH" | "Wire";
  status: "scheduled" | "executed" | "failed" | "reconciled";
  executedAt?: string;
};

export type KPI = {
  label: string;
  value: string | number;
  trend?: "up" | "down" | "flat";
  hint?: string;
};

export type P2PStage =
  | "Need Identification"
  | "Requisition"
  | "Sourcing"
  | "PO Creation"
  | "Dispatch"
  | "Receipt"
  | "Invoicing"
  | "Payment";

