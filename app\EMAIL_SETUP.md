# Email Configuration for RFQ Sending

This guide explains how to configure email sending functionality for the Procurement Assistant's RFQ feature.

## Overview

The RFQ email feature allows users to:
- Send generated RFQs directly via email to vendors
- Include RFQ details, specifications, and attachments
- Track email sending status and history
- Auto-populate vendor email addresses

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```env
# Email Configuration for RFQ sending
SMTP_HOST="smtp.gmail.com"                    # Your SMTP server host
SMTP_PORT="587"                               # SMTP port (587 for TLS, 465 for SSL)
SMTP_SECURE="false"                           # Set to "true" for SSL (port 465)
SMTP_USER="<EMAIL>"              # SMTP username/email
SMTP_PASS="your-app-password"                 # SMTP password or app password
EMAIL_FROM_NAME="Procurement Assistant"        # Display name for sent emails
EMAIL_FROM_ADDRESS="<EMAIL>"     # From email address
```

### Common SMTP Providers

#### Gmail
```env
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"  # Use App Password, not regular password
```

**Note**: For Gmail, you need to:
1. Enable 2-factor authentication
2. Generate an App Password: https://support.google.com/accounts/answer/185833
3. Use the App Password in `SMTP_PASS`

#### Outlook/Hotmail
```env
SMTP_HOST="smtp-mail.outlook.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-password"
```

#### Custom SMTP Server
```env
SMTP_HOST="mail.yourdomain.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-password"
```

## Development Mode

If SMTP is not configured, the system will run in development mode:
- Email content is logged to the console
- No actual emails are sent
- All email functionality works for testing UI/UX

## Production Setup

For production deployment:

1. **Install nodemailer** (when ready for production):
   ```bash
   cd app
   npm install nodemailer @types/nodemailer
   ```

2. **Uncomment SMTP code** in `app/api/email-service.ts`:
   - Remove the mock implementation
   - Uncomment the nodemailer code block
   - Test thoroughly before deployment

3. **Security considerations**:
   - Use environment variables for all credentials
   - Consider using OAuth2 for Gmail
   - Use dedicated email service accounts
   - Monitor email sending limits

## Features

### Email Templates
- Professional HTML email templates
- Responsive design for mobile devices
- Includes all RFQ details and specifications
- Branded with company information

### Recipient Management
- Auto-populate from invited vendors
- Manual email address entry
- Validation of email addresses
- Support for multiple recipients

### Email Tracking
- Track email sending status (pending, sending, sent, failed)
- Email history per RFQ
- Error reporting and notifications
- Retry functionality for failed sends

### Attachments
- Option to include RFQ attachments
- PDF generation of RFQ content
- File size and type validation

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Check SMTP credentials
   - For Gmail, ensure App Password is used
   - Verify 2FA is enabled for Gmail

2. **Connection Timeout**
   - Check SMTP host and port
   - Verify firewall settings
   - Try different ports (587, 465, 25)

3. **Emails Not Received**
   - Check spam/junk folders
   - Verify recipient email addresses
   - Check email provider limits

4. **SSL/TLS Errors**
   - Try different SMTP_SECURE settings
   - Check if your provider requires SSL/TLS
   - Verify port configuration

### Testing

1. **Check Configuration**:
   ```bash
   curl http://localhost:3101/api/email/config
   ```

2. **Test Email Sending**:
   - Create a test RFQ
   - Send to your own email address
   - Check console logs for errors

3. **Monitor Logs**:
   - Check server console for SMTP logs
   - Look for authentication errors
   - Verify email content generation

## Support

For additional help:
- Check the server console logs
- Verify environment variables are loaded
- Test with a simple email service first
- Consider using email service providers like SendGrid or Mailgun for production

## Security Notes

- Never commit SMTP credentials to version control
- Use strong, unique passwords for email accounts
- Consider using OAuth2 for enhanced security
- Monitor email sending for abuse
- Implement rate limiting for production use
