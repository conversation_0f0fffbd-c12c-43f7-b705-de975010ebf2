# Troubleshooting Guide: Node.js Development Environment

This guide helps resolve common issues when running `npm run dev` in the procurement agent application.

## 🚨 Common Error Patterns

### 1. Primary Error: `Cannot find package 'pg'`

**Error Message:**
```
Error [ERR_MODULE_NOT_FOUND]: Cannot find package 'pg' imported from C:\...\api\database\config.ts
```

**Root Cause:** Missing PostgreSQL client library

**Solutions:**

```bash
# Option 1: Install missing packages
npm install pg @types/pg

# Option 2: Clean install if corrupted
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
npm install pg @types/pg

# Option 3: Use Yarn if npm fails
yarn add pg @types/pg
```

### 2. Secondary Error: Frontend Proxy Failures

**Error Message:**
```
AggregateError [ECONNREFUSED] at internalConnectMultiple (node:net)
```

**Root Cause:** Backend server is down due to primary error

**Solutions:**
1. Fix the primary error first
2. Verify backend server starts on port 3101
3. Check Vite proxy configuration

## 🔧 Step-by-Step Resolution

### Step 1: Verify Environment

```bash
# Check Node.js version (requires >= 18.0.0)
node --version

# Check npm version
npm --version

# Run dependency check
npm run check-deps
```

### Step 2: Install Dependencies

```bash
# Clean installation
npm cache clean --force
rm -rf node_modules package-lock.json

# Fresh install
npm install

# Install PostgreSQL packages specifically
npm install pg @types/pg
```

### Step 3: Environment Configuration

Create `.env` file in the `app` directory:

```env
# Database Configuration (Required for full functionality)
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=procure_agent
DATABASE_USER=your_username
DATABASE_PASSWORD=your_password
DATABASE_SSL=false

# Optional: Full connection string
DATABASE_URL=postgresql://username:password@localhost:5432/procure_agent

# API Keys (Optional but recommended)
OPENAI_API_KEY=your_openai_key
PERPLEXITY_API_KEY=your_perplexity_key

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

### Step 4: Database Setup (Optional)

If you want full functionality with result persistence:

```bash
# Install PostgreSQL (if not installed)
# Windows: Download from postgresql.org
# macOS: brew install postgresql
# Ubuntu: sudo apt install postgresql postgresql-contrib

# Create database
createdb procure_agent

# Run database setup
npm run db:setup
```

### Step 5: Start Development Server

```bash
# Start with dependency checking
npm run dev

# Or start components separately for debugging
npm run dev:server  # Backend only
npm run dev:vite    # Frontend only
```

## 🛠️ Advanced Troubleshooting

### Port Conflicts

If you get `EADDRINUSE` errors:

```bash
# Check what's using the ports
netstat -ano | findstr :3100  # Frontend port
netstat -ano | findstr :3101  # Backend port

# Kill processes if needed (Windows)
taskkill /PID <process_id> /F

# Or change ports in configuration
# Frontend: vite.config.js (server.port)
# Backend: api/index.ts or environment variable
```

### Module Resolution Issues

```bash
# Clear all caches
npm cache clean --force
rm -rf node_modules package-lock.json
rm -rf dist

# Reinstall with exact versions
npm ci  # Uses package-lock.json exactly

# Or force reinstall
npm install --force
```

### TypeScript Issues

```bash
# Check TypeScript configuration
npx tsc --noEmit

# Rebuild TypeScript
npm run build

# Clear TypeScript cache
rm -rf dist
rm -rf .tsbuildinfo
```

## 🔍 Diagnostic Commands

### Check System Status

```bash
# Run comprehensive dependency check
npm run check-deps

# Check database connectivity (if configured)
npm run db:info

# Test individual components
npm run dev:server  # Test backend only
npm run dev:vite    # Test frontend only
```

### Debug Network Issues

```bash
# Test if ports are accessible
telnet localhost 3101  # Backend
telnet localhost 3100  # Frontend

# Check firewall settings
# Windows: Windows Defender Firewall
# macOS: System Preferences > Security & Privacy > Firewall
# Linux: sudo ufw status
```

## 🚀 Prevention Strategies

### 1. Pre-commit Hooks

Add to `package.json`:

```json
{
  "husky": {
    "hooks": {
      "pre-commit": "npm run check-deps && npm run lint"
    }
  }
}
```

### 2. Docker Environment (Recommended)

Create `docker-compose.yml`:

```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3100:3100"
      - "3101:3101"
    environment:
      - NODE_ENV=development
    volumes:
      - .:/app
      - /app/node_modules
  
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: procure_agent
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

### 3. Regular Maintenance

```bash
# Weekly dependency updates
npm outdated
npm update

# Security audit
npm audit
npm audit fix

# Clean unused packages
npm prune
```

## 📋 Quick Reference

### Essential Commands

```bash
# Development
npm run dev              # Start full development environment
npm run check-deps       # Check all dependencies
npm run db:setup         # Set up database (if needed)

# Debugging
npm run dev:server       # Backend only
npm run dev:vite         # Frontend only
npm list pg @types/pg    # Check specific packages

# Maintenance
npm cache clean --force  # Clear npm cache
npm install --force      # Force reinstall
npm ci                   # Clean install from lock file
```

### Common File Locations

- **Environment**: `app/.env`
- **Dependencies**: `app/package.json`
- **Vite Config**: `app/vite.config.js`
- **Backend Entry**: `app/api/index.ts`
- **Database Config**: `app/api/database/config.ts`

### Port Configuration

- **Frontend (Vite)**: 3100
- **Backend (Hono)**: 3101
- **PostgreSQL**: 5432

## 🆘 Getting Help

If issues persist:

1. **Check logs**: Look for detailed error messages in terminal output
2. **Run diagnostics**: Use `npm run check-deps` for comprehensive analysis
3. **Verify environment**: Ensure all required environment variables are set
4. **Test components**: Start backend and frontend separately to isolate issues
5. **Check documentation**: Review `DATABASE_SETUP.md` for database-specific issues

### Common Solutions Summary

| Error | Quick Fix |
|-------|-----------|
| `Cannot find package 'pg'` | `npm install pg @types/pg` |
| `ECONNREFUSED` | Fix backend error first |
| `EADDRINUSE` | Kill process using port or change port |
| `MODULE_NOT_FOUND` | `npm install` or `npm ci` |
| Database connection failed | Check PostgreSQL service and credentials |
| TypeScript errors | `npx tsc --noEmit` and fix type issues |

Remember: Most issues are resolved by ensuring all dependencies are installed and the environment is properly configured.
