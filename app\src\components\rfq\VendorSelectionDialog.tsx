import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import type { Vendor } from "@/types/p2p";
import { useState } from "react";

export function VendorSelectionDialog({ vendors, selected, onChange }: { vendors: Vendor[]; selected: string[]; onChange: (ids: string[]) => void }) {
  const [open, setOpen] = useState(false);
  const [temp, setTemp] = useState<string[]>(selected);
  return (
    <Dialog open={open} onOpenChange={(v) => { setOpen(v); if (v) setTemp(selected); }}>
      <DialogTrigger asChild>
        <Button variant="outline">Invite Vendors ({selected.length})</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Select Vendors</DialogTitle>
        </DialogHeader>
        <div className="max-h-[300px] overflow-auto space-y-2">
          {vendors.map(v => (
            <label key={v.id} className="flex items-center gap-2">
              <input type="checkbox" checked={temp.includes(v.id)} onChange={(e) => setTemp(prev => e.target.checked ? [...prev, v.id] : prev.filter(id => id !== v.id))} />
              <span>{v.name}</span>
            </label>
          ))}
          {vendors.length === 0 && <p className="text-sm text-muted-foreground">No vendors</p>}
        </div>
        <div className="flex justify-end gap-2">
          <Button variant="ghost" onClick={() => setOpen(false)}>Cancel</Button>
          <Button onClick={() => { onChange(temp); setOpen(false); }}>Apply</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

