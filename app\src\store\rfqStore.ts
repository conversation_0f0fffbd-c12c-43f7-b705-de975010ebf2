import { create } from "zustand";
import type { UUID, Vendor } from "@/types/p2p";

// RFQ domain for UI-rich experience
export type RFQStatus = "Draft" | "Sent" | "Responded" | "Awarded" | "Closed";

export type PartyInfo = {
  company: string;
  contactName?: string;
  email?: string;
  phone?: string;
  address?: string;
};

export type Attachment = {
  id: UUID;
  name: string;
  url?: string;
  type?: string;
};

export type RFQLineItem = {
  id: UUID;
  itemName: string;
  partNumber?: string;
  quantity: number;
  unit?: string;
  materialSpec?: string;
  dueDate?: string; // ISO date
  notes?: string;
  imageUrl?: string;
};

export type RFQ = {
  id: UUID;
  refNumber: string;
  title: string;
  description?: string;
  status: RFQStatus;
  client: PartyInfo;
  vendor?: PartyInfo;
  createdAt: string; // ISO
  dueDate?: string; // ISO
  responseDeadline?: string; // ISO
  notes?: string;
  attachments: Attachment[];
  lineItems: RFQLineItem[];
  invitedVendorIds: UUID[];
  history: HistoryEntry[];
};

export type ItemPriceQuote = {
  lineItemId: UUID;
  unitPrice: number;
  currency: string;
  notes?: string;
};

export type VendorQuote = {
  id: UUID;
  rfqId: UUID;
  vendorId: UUID;
  items: ItemPriceQuote[];
  total: number;
  currency: string;
  deliveryTerms?: string;
  deliveryDate?: string; // ISO
  attachments?: Attachment[];
  notes?: string;
  status: "Submitted" | "RevisionRequested" | "Rejected" | "Awarded";
  submittedAt: string;
};

export type HistoryEntry = {
  id: UUID;
  rfqId: UUID;
  at: string; // ISO
  user?: string;
  action:
    | "Created"
    | "Modified"
    | "Sent"
    | "Responded"
    | "Awarded"
    | "Closed"
    | "AI_Generated"
    | "RevisionRequested"
    | "Rejected";
  details?: string;
};

export type GeneratedReport = {
  id: UUID;
  rfqId: UUID;
  title: string;
  sections: Array<{ heading: string; markdown?: string; html?: string }>; // UI renders via components, but keep copy for export
  createdAt: string;
};

export type Notification = {
  id: UUID;
  type: "info" | "success" | "warning" | "error";
  title: string;
  message?: string;
  createdAt: string;
  read?: boolean;
};

export type AIOutputPayload = {
  type: "rfq" | "report" | "proposal";
  rfq?: Partial<RFQ> & { lineItems?: Partial<RFQLineItem>[] };
  report?: { title: string; sections: Array<{ heading: string; markdown?: string; html?: string }> };
  proposal?: Partial<VendorQuote> & { items?: Partial<ItemPriceQuote>[] };
  context?: string;
};

export type EmailStatus = "pending" | "sending" | "sent" | "failed";

export type EmailRecord = {
  id: UUID;
  rfqId: UUID;
  recipients: string[];
  subject: string;
  message: string;
  status: EmailStatus;
  sentAt?: string;
  error?: string;
  includeAttachments: boolean;
};

export type EmailData = {
  rfqId: string;
  recipients: string[];
  subject: string;
  message: string;
  includeAttachments: boolean;
};

export type VendorSearchResult = {
  vendors: Vendor[];
  total: number;
  hasMore: boolean;
  query?: string;
};

export type VendorSearchState = {
  isSearching: boolean;
  searchResults: VendorSearchResult | null;
  selectedVendors: Vendor[];
  searchQuery: string;
};

export type RFQState = {
  vendors: Vendor[];
  rfqs: RFQ[];
  quotes: VendorQuote[];
  reports: GeneratedReport[];
  notifications: Notification[];
  emailRecords: EmailRecord[];
  vendorSearch: VendorSearchState;

  // Actions
  bootstrapDemo: () => void;
  addVendor: (v: Vendor) => void;

  createRFQ: (payload: Omit<RFQ, "history" | "status" | "createdAt">) => RFQ;
  updateRFQ: (id: UUID, patch: Partial<RFQ>) => void;
  sendRFQ: (id: UUID) => void;
  closeRFQ: (id: UUID) => void;

  addLineItem: (rfqId: UUID, li: Omit<RFQLineItem, "id">) => RFQLineItem;
  updateLineItem: (rfqId: UUID, lineItemId: UUID, patch: Partial<RFQLineItem>) => void;
  removeLineItem: (rfqId: UUID, lineItemId: UUID) => void;

  inviteVendors: (rfqId: UUID, vendorIds: UUID[]) => void;
  addAttachment: (rfqId: UUID, file: Omit<Attachment, "id">) => Attachment;
  removeAttachment: (rfqId: UUID, attachmentId: UUID) => void;

  submitQuote: (quote: Omit<VendorQuote, "id" | "status" | "submittedAt">) => VendorQuote;
  awardQuote: (rfqId: UUID, vendorId: UUID) => void;
  requestRevision: (rfqId: UUID, vendorId: UUID, message?: string) => void;
  rejectQuote: (rfqId: UUID, vendorId: UUID, reason?: string) => void;

  addHistory: (entry: Omit<HistoryEntry, "id" | "at">) => void;
  addReport: (report: Omit<GeneratedReport, "id" | "createdAt">) => GeneratedReport;
  notify: (n: Omit<Notification, "id" | "createdAt" | "read">) => void;

  // Email actions
  sendRFQEmail: (emailData: EmailData) => Promise<EmailRecord>;
  updateEmailStatus: (emailId: UUID, status: EmailStatus, error?: string) => void;
  getEmailHistory: (rfqId: UUID) => EmailRecord[];

  // Vendor search actions
  searchVendors: (query: string, limit?: number, offset?: number) => Promise<void>;
  clearVendorSearch: () => void;
  selectVendor: (vendor: Vendor) => void;
  unselectVendor: (vendorId: UUID) => void;
  clearSelectedVendors: () => void;
  setSearchQuery: (query: string) => void;

  ingestAIResponse: (payload: AIOutputPayload) => { rfqId?: UUID; reportId?: UUID };

  // Phase 1 persistence
  loadAll?: () => Promise<void>;
  saveRFQ?: (rfq: RFQ) => Promise<void>;
  saveVendor?: (vendor: Vendor) => Promise<void>;
  saveQuote?: (quote: VendorQuote) => Promise<void>;
  saveReport?: (report: GeneratedReport) => Promise<void>;
  syncRFQ?: (id: UUID) => Promise<void>;

  // Helpers
  remapVendorId?: (oldId: UUID, newId: UUID) => void;
};

function uid(): UUID {
  // In-browser UID; replace with crypto.randomUUID when available
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  return (globalThis.crypto?.randomUUID?.() as string) || `id_${Math.random().toString(36).slice(2, 10)}`;
}

export const useRFQStore = create<RFQState>((set, get) => ({
  vendors: [],
  rfqs: [],
  quotes: [],
  reports: [],
  notifications: [],
  emailRecords: [],
  vendorSearch: {
    isSearching: false,
    searchResults: null,
    selectedVendors: [],
    searchQuery: '',
  },

  // Phase 1 persistence methods (implemented lazily to avoid breaking existing UI)
  async loadAll() {
    try {
      const [rfqs, vendors, quotes, reports] = await Promise.all([
        (await fetch('/api/rfqs')).json().catch(()=>[]),
        (await fetch('/api/vendors')).json().catch(()=>[]),
        (await fetch('/api/quotes')).json().catch(()=>[]),
        (await fetch('/api/reports')).json().catch(()=>[]),
      ]);
      set({ rfqs, vendors, quotes, reports });
    } catch (e) {
      console.warn('loadAll failed', e);
    }
  },
  async saveRFQ(rfq) {
    try {
      await fetch('/api/rfqs', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(rfq) });
    } catch (e) { console.warn('saveRFQ failed', e); }
  },
  async saveVendor(vendor) {
    try {
      const isCreate = !vendor.id || (typeof vendor.id === 'string' && vendor.id.startsWith('temp_'));
      const url = isCreate ? `/api/vendors` : `/api/vendors/${vendor.id}`;
      const method = isCreate ? 'POST' : 'PATCH';
      const res = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...(!isCreate && vendor.etag ? { 'If-Match': vendor.etag } : {}),
        },
        body: JSON.stringify(vendor),
      });
      if (res.status === 409) {
        const latest = await res.json();
        const etag = res.headers.get('etag') || undefined;
        set(s => ({ vendors: s.vendors.map(v => v.id === latest.id ? { ...latest, etag } : v) }));
        get().notify({ type: 'warning', title: 'Vendor update conflict', message: `We loaded the latest copy for ${latest.name}. Please retry your change.` });
        return;
      }
      if (res.ok) {
        const saved = await res.json();
        const etag = res.headers.get('etag') || undefined;
        const savedWithMeta = { ...saved, etag } as Vendor;
        set(s => {
          const list = [...s.vendors];
          const idxByOld = list.findIndex(v => v.id === vendor.id);
          if (idxByOld >= 0) {
            const oldId = vendor.id;
            list[idxByOld] = savedWithMeta;
            if (oldId !== savedWithMeta.id) {
              // remap across store
              const remappedRfqs = s.rfqs.map(r => ({
                ...r,
                invitedVendorIds: r.invitedVendorIds.map(id => id === oldId ? savedWithMeta.id : id),
              }));
              const remappedQuotes = s.quotes.map(q => (q.vendorId === oldId ? { ...q, vendorId: savedWithMeta.id } : q));
              return { vendors: list, rfqs: remappedRfqs, quotes: remappedQuotes };
            }
          } else {
            const idxByNew = list.findIndex(v => v.id === savedWithMeta.id);
            if (idxByNew >= 0) list[idxByNew] = savedWithMeta; else list.unshift(savedWithMeta);
          }
          return { vendors: list };
        });
      }
    } catch (e) { console.warn('saveVendor failed', e); }
  },
  remapVendorId(oldId, newId) {
    set(s => ({
      rfqs: s.rfqs.map(r => ({
        ...r,
        invitedVendorIds: r.invitedVendorIds.map(id => id === oldId ? newId : id),
        history: r.history,
      })),
      quotes: s.quotes.map(q => (q.vendorId === oldId ? { ...q, vendorId: newId } : q)),
    }));
  },

  async saveQuote(quote) {
    try {
      await fetch('/api/quotes', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(quote) });
    } catch (e) { console.warn('saveQuote failed', e); }
  },
  async saveReport(report) {
    try {
      await fetch('/api/reports', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(report) });
    } catch (e) { console.warn('saveReport failed', e); }
  },
  async syncRFQ(id) {
    try {
      const rfq = get().rfqs.find(r => r.id === id);
      if (!rfq) return;
      await fetch(`/api/rfqs/${id}`, { method: 'PATCH', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(rfq) });
    } catch (e) { console.warn('syncRFQ failed', e); }
  },

  bootstrapDemo: () => {
    const demoVendor: Vendor = { id: "v1", name: "EcoPackage India" };
    set(s => ({ vendors: [demoVendor] }));
  },

  addVendor: (v) => {
    set(s => ({ vendors: [...s.vendors, v] }));
    get().saveVendor?.(v);
  },

  createRFQ: (payload) => {
    const rfq: RFQ = {
      ...payload,
      id: payload.id || uid(),
      status: "Draft",
      createdAt: new Date().toISOString(),
      history: [],
    } as RFQ;
    set(s => ({ rfqs: [rfq, ...s.rfqs] }));
    get().addHistory({ rfqId: rfq.id, action: "Created", details: `RFQ ${rfq.refNumber || rfq.id}` });
    // Persist
    get().saveRFQ?.(rfq);
    return rfq;
  },

  updateRFQ: (id, patch) => set(s => ({
    rfqs: s.rfqs.map(r => (r.id === id ? { ...r, ...patch } : r)),
  }), false, 'rfq/updateRFQ'),

  sendRFQ: (id) => set(s => {
    const rfq = s.rfqs.find(r => r.id === id);
    if (!rfq) return {} as any;
    const updated = { ...rfq, status: "Sent" as RFQStatus };
    // Persist
    get().syncRFQ?.(id);
    return { rfqs: s.rfqs.map(r => (r.id === id ? updated : r)) };
  }, false, "rfq/sendRFQ"),

  closeRFQ: (id) => set(s => ({
    rfqs: s.rfqs.map(r => (r.id === id ? { ...r, status: "Closed" as RFQStatus } : r)),
  }), false, 'rfq/closeRFQ'),

  addLineItem: (rfqId, li) => {
    const line: RFQLineItem = { id: uid(), ...li };
    set(s => ({
      rfqs: s.rfqs.map(r => (r.id === rfqId ? { ...r, lineItems: [...r.lineItems, line] } : r)),
    }));
    get().addHistory({ rfqId, action: "Modified", details: `Added line ${line.itemName}` });
    return line;
  },

  updateLineItem: (rfqId, lineItemId, patch) => set(s => ({
    rfqs: s.rfqs.map(r => (r.id === rfqId ? {
      ...r,
      lineItems: r.lineItems.map(li => (li.id === lineItemId ? { ...li, ...patch } : li)),
    } : r)),
  })),

  removeLineItem: (rfqId, lineItemId) => set(s => ({
    rfqs: s.rfqs.map(r => (r.id === rfqId ? {
      ...r,
      lineItems: r.lineItems.filter(li => li.id !== lineItemId),
    } : r)),
  })),

  inviteVendors: (rfqId, vendorIds) => set(s => ({
    rfqs: s.rfqs.map(r => (r.id === rfqId ? {
      ...r,
      invitedVendorIds: Array.from(new Set([...(r.invitedVendorIds || []), ...vendorIds])),
    } : r)),
  })),

  addAttachment: (rfqId, file) => {
    const att: Attachment = { id: uid(), ...file };
    set(s => ({
      rfqs: s.rfqs.map(r => (r.id === rfqId ? { ...r, attachments: [...r.attachments, att] } : r)),
    }));
    return att;
  },

  removeAttachment: (rfqId, attachmentId) => set(s => ({
    rfqs: s.rfqs.map(r => (r.id === rfqId ? {
      ...r,
      attachments: r.attachments.filter(a => a.id !== attachmentId),
    } : r)),
  })),

  submitQuote: (quote) => {
    const q: VendorQuote = {
      ...quote,
      id: uid(),
      status: "Submitted",
      submittedAt: new Date().toISOString(),
    } as VendorQuote;
    set(s => ({ quotes: [q, ...s.quotes] }));
    get().addHistory({ rfqId: q.rfqId, action: "Responded", details: `Vendor ${q.vendorId} submitted quote` });
    return q;
  },

  awardQuote: (rfqId, vendorId) => set(s => ({
    quotes: s.quotes.map(q => (q.rfqId === rfqId ? { ...q, status: q.vendorId === vendorId ? "Awarded" : q.status } : q)),
    rfqs: s.rfqs.map(r => (r.id === rfqId ? { ...r, status: "Awarded" as RFQStatus } : r)),
  })),

  requestRevision: (rfqId, vendorId, message) => set(s => ({
    quotes: s.quotes.map(q => (q.rfqId === rfqId && q.vendorId === vendorId ? { ...q, status: "RevisionRequested" } : q)),
  }), false, "rfq/requestRevision"),

  rejectQuote: (rfqId, vendorId, reason) => set(s => ({
    quotes: s.quotes.map(q => (q.rfqId === rfqId && q.vendorId === vendorId ? { ...q, status: "Rejected" } : q)),
  }), false, "rfq/rejectQuote"),

  addHistory: (entry) => set(s => ({
    rfqs: s.rfqs.map(r => (r.id === entry.rfqId ? {
      ...r,
      history: [
        { id: uid(), at: new Date().toISOString(), ...entry },
        ...r.history,
      ],
    } : r)),
  })),

  addReport: (report) => {
    const rep: GeneratedReport = {
      ...report,
      id: uid(),
      createdAt: new Date().toISOString(),
    };
    set(s => ({ reports: [rep, ...s.reports] }));
    return rep;
  },

  notify: (n) => set(s => ({
    notifications: [
      { id: uid(), createdAt: new Date().toISOString(), read: false, ...n },
      ...s.notifications,
    ],
  })),

  // Email actions
  sendRFQEmail: async (emailData) => {
    const emailRecord: EmailRecord = {
      id: uid(),
      rfqId: emailData.rfqId as UUID,
      recipients: emailData.recipients,
      subject: emailData.subject,
      message: emailData.message,
      status: "sending",
      includeAttachments: emailData.includeAttachments,
    };

    // Add email record to store
    set(s => ({
      emailRecords: [emailRecord, ...s.emailRecords],
    }));

    try {
      // Call the email API
      const response = await fetch('/api/email/send-rfq', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(emailData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Failed to send email' }));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      // Update status to sent
      const sentRecord = {
        ...emailRecord,
        status: "sent" as EmailStatus,
        sentAt: new Date().toISOString(),
      };

      set(s => ({
        emailRecords: s.emailRecords.map(e => e.id === emailRecord.id ? sentRecord : e),
      }));

      // Add success notification
      get().notify({
        type: "success",
        title: "RFQ Email Sent",
        message: `Successfully sent to ${emailData.recipients.length} recipient${emailData.recipients.length !== 1 ? 's' : ''}`,
      });

      return sentRecord;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      // Update status to failed
      const failedRecord = {
        ...emailRecord,
        status: "failed" as EmailStatus,
        error: errorMessage,
      };

      set(s => ({
        emailRecords: s.emailRecords.map(e => e.id === emailRecord.id ? failedRecord : e),
      }));

      // Add error notification
      get().notify({
        type: "error",
        title: "Email Send Failed",
        message: errorMessage,
      });

      throw error;
    }
  },

  updateEmailStatus: (emailId, status, error) => set(s => ({
    emailRecords: s.emailRecords.map(e =>
      e.id === emailId
        ? { ...e, status, error, ...(status === "sent" ? { sentAt: new Date().toISOString() } : {}) }
        : e
    ),
  })),

  getEmailHistory: (rfqId) => {
    return get().emailRecords.filter(e => e.rfqId === rfqId);
  },

  // Vendor search actions
  searchVendors: async (query, limit = 50, offset = 0) => {
    set(s => ({
      vendorSearch: { ...s.vendorSearch, isSearching: true, searchQuery: query }
    }));

    try {
      const params = new URLSearchParams({
        q: query,
        limit: limit.toString(),
        offset: offset.toString(),
      });

      const response = await fetch(`/api/vendors/search?${params}`);
      if (!response.ok) {
        throw new Error(`Search failed: ${response.statusText}`);
      }

      const searchResults: VendorSearchResult = await response.json();

      set(s => ({
        vendorSearch: {
          ...s.vendorSearch,
          isSearching: false,
          searchResults,
        }
      }));
    } catch (error) {
      console.error('Vendor search failed:', error);
      set(s => ({
        vendorSearch: {
          ...s.vendorSearch,
          isSearching: false,
          searchResults: null,
        }
      }));

      get().notify({
        type: "error",
        title: "Search Failed",
        message: error instanceof Error ? error.message : "Failed to search vendors",
      });
    }
  },

  clearVendorSearch: () => set(s => ({
    vendorSearch: {
      ...s.vendorSearch,
      searchResults: null,
      searchQuery: '',
    }
  })),

  selectVendor: (vendor) => set(s => ({
    vendorSearch: {
      ...s.vendorSearch,
      selectedVendors: s.vendorSearch.selectedVendors.find(v => v.id === vendor.id)
        ? s.vendorSearch.selectedVendors
        : [...s.vendorSearch.selectedVendors, vendor]
    }
  })),

  unselectVendor: (vendorId) => set(s => ({
    vendorSearch: {
      ...s.vendorSearch,
      selectedVendors: s.vendorSearch.selectedVendors.filter(v => v.id !== vendorId)
    }
  })),

  clearSelectedVendors: () => set(s => ({
    vendorSearch: {
      ...s.vendorSearch,
      selectedVendors: []
    }
  })),

  setSearchQuery: (query) => set(s => ({
    vendorSearch: {
      ...s.vendorSearch,
      searchQuery: query
    }
  })),

  ingestAIResponse: (payload) => {
    const result: { rfqId?: UUID; reportId?: UUID } = {};
    if (payload.type === "rfq" && payload.rfq) {
      // Create or update an RFQ from AI suggestion
      const patch = payload.rfq;
      const rfq: RFQ = {
        id: (patch.id as UUID) || uid(),
        refNumber: patch.refNumber || `RFQ-${Date.now()}`,
        title: patch.title || "Untitled RFQ",
        description: patch.description,
        status: (patch.status as RFQStatus) || "Draft",
        client: patch.client || { company: "" },
        vendor: patch.vendor,
        createdAt: patch.createdAt || new Date().toISOString(),
        dueDate: patch.dueDate,
        responseDeadline: patch.responseDeadline,
        notes: patch.notes,
        attachments: patch.attachments || [],
        lineItems: (patch.lineItems || []).map((li) => ({
          id: (li.id as UUID) || uid(),
          itemName: li.itemName || li.description || "Item",
          partNumber: li.partNumber,
          quantity: li.quantity || 1,
          unit: li.unit,
          materialSpec: li.materialSpec,
          dueDate: li.dueDate,
          notes: li.notes,
          imageUrl: li.imageUrl,
        })),
        invitedVendorIds: patch.invitedVendorIds || [],
        history: [],
      };
      set(s => ({ rfqs: [rfq, ...s.rfqs] }));
      get().addHistory({ rfqId: rfq.id, action: "AI_Generated", details: payload.context || "AI suggested RFQ" });
      result.rfqId = rfq.id;
    }

    if (payload.type === "report" && payload.report) {
      const latestRFQ = get().rfqs[0];
      const rep = get().addReport({
        rfqId: latestRFQ?.id || ("unassigned" as UUID),
        title: payload.report.title,
        sections: payload.report.sections,
      });
      result.reportId = rep.id;
      if (latestRFQ?.id) get().addHistory({ rfqId: latestRFQ.id, action: "AI_Generated", details: `Report: ${rep.title}` });
    }

    if (payload.type === "proposal" && payload.proposal) {
      const q = get().submitQuote({
        rfqId: payload.proposal.rfqId as UUID,
        vendorId: payload.proposal.vendorId as UUID,
        items: (payload.proposal.items || []).map(i => ({
          lineItemId: i.lineItemId as UUID,
          unitPrice: i.unitPrice || 0,
          currency: i.currency || "USD",
          notes: i.notes,
        })),
        total: payload.proposal.total || 0,
        currency: payload.proposal.currency || "USD",
        deliveryTerms: payload.proposal.deliveryTerms,
        deliveryDate: payload.proposal.deliveryDate,
        attachments: payload.proposal.attachments,
        notes: payload.proposal.notes,
      });
      result.rfqId = q.rfqId;
    }

    // Notify
    get().notify({ type: "success", title: "AI output ingested", message: payload.type });
    return result;
  },
}));

