import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { TrendingUp, <PERSON><PERSON>heck, Banknote, Timer, CheckCircle2 } from "lucide-react";
import type { KPI, P2PStage } from "@/types/p2p";

export function P2POverviewCard({
  stage = "Need Identification",
  kpis = [],
  summary = {
    costSavings: "₹8.7L",
    efficiency: "23% faster cycle",
    suppliers: "247 active",
    compliance: "98% policy adherence",
  },
}: {
  stage?: P2PStage;
  kpis?: KPI[];
  summary?: { costSavings: string; efficiency: string; suppliers: string; compliance: string };
}) {
  const stages: P2PStage[] = [
    "Need Identification",
    "Requisition",
    "Sourcing",
    "PO Creation",
    "Dispatch",
    "Receipt",
    "Invoicing",
    "Payment",
  ];

  const currentIndex = stages.indexOf(stage);
  const progress = ((currentIndex + 1) / stages.length) * 100;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Procure-to-Pay Cycle</CardTitle>
        <CardDescription>
          From need identification to payment — with built-in cost, efficiency, supplier and compliance benefits.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Stage Progress */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-muted-foreground">Current Stage</span>
            <Badge variant="outline">{stage}</Badge>
          </div>
          <Progress value={progress} />
          <div className="flex flex-wrap gap-2 mt-2">
            {stages.map((s, i) => (
              <Badge key={s} variant={i <= currentIndex ? "default" : "secondary"}>
                {s}
              </Badge>
            ))}
          </div>
        </div>

        <Separator />

        {/* KPIs */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {(kpis.length ? kpis : [
            { label: "Cycle Time", value: "7.8 days", trend: "down", hint: "Last 30 days" },
            { label: "On-Contract Spend", value: "82%", trend: "up" },
            { label: "3-Way Match Rate", value: "96%", trend: "up" },
            { label: "First-Time-Right", value: "91%", trend: "up" },
          ]).map((k) => (
            <div key={k.label} className="p-4 rounded-lg border bg-card/50">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{k.label}</span>
                {k.trend === "up" && <TrendingUp className="h-4 w-4 text-emerald-500" />}
                {k.trend === "down" && <Timer className="h-4 w-4 text-amber-500" />}
              </div>
              <div className="text-2xl font-semibold">{k.value}</div>
              {k.hint && <div className="text-xs text-muted-foreground">{k.hint}</div>}
            </div>
          ))}
        </div>

        <Separator />

        {/* Summary */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="p-4 rounded-lg border bg-card/50 flex items-center gap-3">
            <Banknote className="h-5 w-5 text-emerald-600" />
            <div>
              <div className="text-sm text-muted-foreground">Cost Savings</div>
              <div className="font-medium">{summary.costSavings}</div>
            </div>
          </div>
          <div className="p-4 rounded-lg border bg-card/50 flex items-center gap-3">
            <Timer className="h-5 w-5 text-blue-600" />
            <div>
              <div className="text-sm text-muted-foreground">Efficiency</div>
              <div className="font-medium">{summary.efficiency}</div>
            </div>
          </div>
          <div className="p-4 rounded-lg border bg-card/50 flex items-center gap-3">
            <CheckCircle2 className="h-5 w-5 text-purple-600" />
            <div>
              <div className="text-sm text-muted-foreground">Suppliers</div>
              <div className="font-medium">{summary.suppliers}</div>
            </div>
          </div>
          <div className="p-4 rounded-lg border bg-card/50 flex items-center gap-3">
            <ShieldCheck className="h-5 w-5 text-emerald-700" />
            <div>
              <div className="text-sm text-muted-foreground">Compliance</div>
              <div className="font-medium">{summary.compliance}</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

