import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useRFQStore } from "@/store/rfqStore";
import { RFQDetailCard } from "./RFQDetailCard";
import { StatusBadge } from "./StatusBadge";
import { VendorEditorDialog } from "./VendorEditorDialog";
import { DevVendorDebugPanel } from "./DevVendorDebugPanel";
import { RFQCreationWizard } from "./RFQCreationWizard";
import { NotificationsPanel } from "./NotificationsPanel";
import type { RFQ } from "@/store/rfqStore";

export function RFQDashboard() {
  const rfqs = useRFQStore(s => s.rfqs);
  const vendors = useRFQStore(s => s.vendors);
  const [activeId, setActiveId] = React.useState<string | null>(null);
  const [showWizard, setShowWizard] = React.useState(false);

  function handleWizardComplete(rfq: RFQ) {
    setActiveId(rfq.id);
    setShowWizard(false);
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div className="lg:col-span-1 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>RFQ Dashboard</CardTitle>
            <CardDescription>Create, manage and track RFQs</CardDescription>
import { VendorEditorDialog } from "./VendorEditorDialog";
import { DevVendorDebugPanel } from "./DevVendorDebugPanel";
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex gap-2 items-center">
              <Button onClick={() => setShowWizard(true)}>Create New RFQ</Button>
              <Input placeholder="Search RFQs..." className="flex-1" />
              <VendorEditorDialog />
              <NotificationsPanel />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>List</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {rfqs.length === 0 && <p className="text-sm text-muted-foreground">No RFQs yet</p>}
            {rfqs.map(r => (
              <button key={r.id} onClick={() => setActiveId(r.id)} className={`w-full text-left p-3 border rounded hover:bg-muted ${activeId===r.id? 'bg-muted': ''}`}>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">{r.title}</div>
                    <div className="text-xs text-muted-foreground">{r.refNumber} • {new Date(r.createdAt).toLocaleDateString()}</div>
                  </div>
                  <StatusBadge status={r.status} />
                </div>
              </button>
            ))}
          </CardContent>
        </Card>
      </div>

      <div className="lg:col-span-2 space-y-4">
        {activeId ? <RFQDetailCard id={activeId} /> : (
          <Card>
            <CardHeader>
              <CardTitle>Details</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Select an RFQ to view details.</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* RFQ Creation Wizard */}
      {showWizard && (
        <RFQCreationWizard
          onClose={() => setShowWizard(false)}
          onComplete={handleWizardComplete}
        />
      )}
    </div>
  );
}

import * as React from "react";

