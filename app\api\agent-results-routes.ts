import { Hono } from 'hono';
import { z } from 'zod';
import { AgentResultService } from './services/agentResultService.js';
import { EmbeddingService } from './services/embeddingService.js';
import {
  CreateAgentResultSchema,
  UpdateAgentResultSchema,
  AgentResultSearchSchema,
  VectorSearchSchema,
  CreateAgentExecutionStepSchema,
  UpdateAgentExecutionStepSchema,
  CreateAgentResultTagSchema,
} from './database/types.js';

const app = new Hono();

function withTraceHeaders(headers: Record<string, string> = {}) {
  const requestId = Math.random().toString(36).slice(2, 10);
  return { ...headers, 'X-Request-Id': requestId };
}

function ok<T>(data: T, status = 200, headers: Record<string, string> = {}) {
  return new Response(JSON.stringify(data), {
    status,
    headers: { 'Content-Type': 'application/json', ...withTraceHeaders(headers) }
  });
}

function bad(message: string, status = 400) {
  return ok({ error: message }, status);
}

const agentResultService = AgentResultService.getInstance();
const embeddingService = EmbeddingService.getInstance();

// Create a new agent result
app.post('/api/agent-results', async (c) => {
  try {
    const body = await c.req.json();
    const parsed = CreateAgentResultSchema.safeParse(body);
    
    if (!parsed.success) {
      return bad(`Invalid request data: ${parsed.error.message}`);
    }

    const result = await agentResultService.createAgentResult(parsed.data);
    return ok(result, 201);
  } catch (error) {
    console.error('Failed to create agent result:', error);
    return bad(error instanceof Error ? error.message : 'Failed to create agent result', 500);
  }
});

// Get agent result by workflow ID
app.get('/api/agent-results/:workflowId', async (c) => {
  try {
    const workflowId = c.req.param('workflowId');
    const result = await agentResultService.getAgentResult(workflowId);
    
    if (!result) {
      return bad('Agent result not found', 404);
    }

    return ok(result);
  } catch (error) {
    console.error('Failed to get agent result:', error);
    return bad(error instanceof Error ? error.message : 'Failed to get agent result', 500);
  }
});

// Update agent result
app.patch('/api/agent-results/:workflowId', async (c) => {
  try {
    const workflowId = c.req.param('workflowId');
    const body = await c.req.json();
    const parsed = UpdateAgentResultSchema.safeParse(body);
    
    if (!parsed.success) {
      return bad(`Invalid request data: ${parsed.error.message}`);
    }

    const result = await agentResultService.updateAgentResult(workflowId, parsed.data);
    
    if (!result) {
      return bad('Agent result not found', 404);
    }

    return ok(result);
  } catch (error) {
    console.error('Failed to update agent result:', error);
    return bad(error instanceof Error ? error.message : 'Failed to update agent result', 500);
  }
});

// Delete agent result
app.delete('/api/agent-results/:workflowId', async (c) => {
  try {
    const workflowId = c.req.param('workflowId');
    const deleted = await agentResultService.deleteAgentResult(workflowId);
    
    if (!deleted) {
      return bad('Agent result not found', 404);
    }

    return ok({ success: true });
  } catch (error) {
    console.error('Failed to delete agent result:', error);
    return bad(error instanceof Error ? error.message : 'Failed to delete agent result', 500);
  }
});

// Search agent results with traditional filters
app.get('/api/agent-results', async (c) => {
  try {
    const query = c.req.query('query');
    const status = c.req.query('status');
    const region = c.req.query('region');
    const startDate = c.req.query('start_date');
    const endDate = c.req.query('end_date');
    const tags = c.req.query('tags');
    const limit = parseInt(c.req.query('limit') || '20');
    const offset = parseInt(c.req.query('offset') || '0');
    const sortBy = c.req.query('sort_by') || 'started_at';
    const sortOrder = c.req.query('sort_order') || 'desc';

    const searchParams = AgentResultSearchSchema.parse({
      query,
      status,
      region,
      start_date: startDate ? new Date(startDate) : undefined,
      end_date: endDate ? new Date(endDate) : undefined,
      tags: tags ? tags.split(',') : undefined,
      limit,
      offset,
      sort_by: sortBy,
      sort_order: sortOrder,
    });

    const results = await agentResultService.searchAgentResults(searchParams);
    return ok(results);
  } catch (error) {
    console.error('Failed to search agent results:', error);
    return bad(error instanceof Error ? error.message : 'Failed to search agent results', 500);
  }
});

// Vector-based similarity search
app.post('/api/agent-results/vector-search', async (c) => {
  try {
    const body = await c.req.json();
    const parsed = VectorSearchSchema.safeParse(body);
    
    if (!parsed.success) {
      return bad(`Invalid request data: ${parsed.error.message}`);
    }

    const results = await agentResultService.vectorSearch(parsed.data);
    return ok(results);
  } catch (error) {
    console.error('Failed to perform vector search:', error);
    return bad(error instanceof Error ? error.message : 'Failed to perform vector search', 500);
  }
});

// Find similar results based on text input
app.post('/api/agent-results/find-similar', async (c) => {
  try {
    const body = await c.req.json();
    const { text, limit = 10, threshold = 0.7 } = body;

    if (!text || typeof text !== 'string') {
      return bad('Text input is required');
    }

    const results = await agentResultService.findSimilarResults(text, limit, threshold);
    return ok(results);
  } catch (error) {
    console.error('Failed to find similar results:', error);
    return bad(error instanceof Error ? error.message : 'Failed to find similar results', 500);
  }
});

// Get recent results
app.get('/api/agent-results/recent', async (c) => {
  try {
    const limit = parseInt(c.req.query('limit') || '20');
    const results = await agentResultService.getRecentResults(limit);
    return ok(results);
  } catch (error) {
    console.error('Failed to get recent results:', error);
    return bad(error instanceof Error ? error.message : 'Failed to get recent results', 500);
  }
});

// Get results by status
app.get('/api/agent-results/status/:status', async (c) => {
  try {
    const status = c.req.param('status') as any;
    const limit = parseInt(c.req.query('limit') || '50');
    
    const results = await agentResultService.getResultsByStatus(status, limit);
    return ok(results);
  } catch (error) {
    console.error('Failed to get results by status:', error);
    return bad(error instanceof Error ? error.message : 'Failed to get results by status', 500);
  }
});

// Get statistics
app.get('/api/agent-results/stats', async (c) => {
  try {
    const stats = await agentResultService.getResultsStats();
    return ok(stats);
  } catch (error) {
    console.error('Failed to get results stats:', error);
    return bad(error instanceof Error ? error.message : 'Failed to get results stats', 500);
  }
});

// Execution steps endpoints

// Create execution step
app.post('/api/agent-results/:workflowId/steps', async (c) => {
  try {
    const workflowId = c.req.param('workflowId');
    const body = await c.req.json();
    
    // Get the result to get result_id
    const agentResult = await agentResultService.getAgentResult(workflowId);
    if (!agentResult) {
      return bad('Agent result not found', 404);
    }

    const stepData = { ...body, result_id: agentResult.id, workflow_id: workflowId };
    const parsed = CreateAgentExecutionStepSchema.safeParse(stepData);
    
    if (!parsed.success) {
      return bad(`Invalid request data: ${parsed.error.message}`);
    }

    const step = await agentResultService.createExecutionStep(parsed.data);
    return ok(step, 201);
  } catch (error) {
    console.error('Failed to create execution step:', error);
    return bad(error instanceof Error ? error.message : 'Failed to create execution step', 500);
  }
});

// Update execution step
app.patch('/api/agent-results/:workflowId/steps/:stepId', async (c) => {
  try {
    const workflowId = c.req.param('workflowId');
    const stepId = c.req.param('stepId');
    const body = await c.req.json();
    
    // Get the result to get result_id
    const agentResult = await agentResultService.getAgentResult(workflowId);
    if (!agentResult) {
      return bad('Agent result not found', 404);
    }

    const parsed = UpdateAgentExecutionStepSchema.safeParse(body);
    
    if (!parsed.success) {
      return bad(`Invalid request data: ${parsed.error.message}`);
    }

    const step = await agentResultService.updateExecutionStep(stepId, agentResult.id, parsed.data);
    
    if (!step) {
      return bad('Execution step not found', 404);
    }

    return ok(step);
  } catch (error) {
    console.error('Failed to update execution step:', error);
    return bad(error instanceof Error ? error.message : 'Failed to update execution step', 500);
  }
});

// Get execution steps for a result
app.get('/api/agent-results/:workflowId/steps', async (c) => {
  try {
    const workflowId = c.req.param('workflowId');
    
    // Get the result to get result_id
    const agentResult = await agentResultService.getAgentResult(workflowId);
    if (!agentResult) {
      return bad('Agent result not found', 404);
    }

    const steps = await agentResultService.getExecutionSteps(agentResult.id);
    return ok(steps);
  } catch (error) {
    console.error('Failed to get execution steps:', error);
    return bad(error instanceof Error ? error.message : 'Failed to get execution steps', 500);
  }
});

// Tag management endpoints

// Add tag to result
app.post('/api/agent-results/:workflowId/tags', async (c) => {
  try {
    const workflowId = c.req.param('workflowId');
    const body = await c.req.json();
    
    // Get the result to get result_id
    const agentResult = await agentResultService.getAgentResult(workflowId);
    if (!agentResult) {
      return bad('Agent result not found', 404);
    }

    const tagData = { ...body, result_id: agentResult.id };
    const parsed = CreateAgentResultTagSchema.safeParse(tagData);
    
    if (!parsed.success) {
      return bad(`Invalid request data: ${parsed.error.message}`);
    }

    const tag = await agentResultService.addTag(parsed.data);
    return ok(tag, 201);
  } catch (error) {
    console.error('Failed to add tag:', error);
    return bad(error instanceof Error ? error.message : 'Failed to add tag', 500);
  }
});

// Get tags for a result
app.get('/api/agent-results/:workflowId/tags', async (c) => {
  try {
    const workflowId = c.req.param('workflowId');
    
    // Get the result to get result_id
    const agentResult = await agentResultService.getAgentResult(workflowId);
    if (!agentResult) {
      return bad('Agent result not found', 404);
    }

    const tags = await agentResultService.getTags(agentResult.id);
    return ok(tags);
  } catch (error) {
    console.error('Failed to get tags:', error);
    return bad(error instanceof Error ? error.message : 'Failed to get tags', 500);
  }
});

// Remove tag from result
app.delete('/api/agent-results/:workflowId/tags/:tagName', async (c) => {
  try {
    const workflowId = c.req.param('workflowId');
    const tagName = c.req.param('tagName');
    
    // Get the result to get result_id
    const agentResult = await agentResultService.getAgentResult(workflowId);
    if (!agentResult) {
      return bad('Agent result not found', 404);
    }

    const removed = await agentResultService.removeTag(agentResult.id, tagName);
    
    if (!removed) {
      return bad('Tag not found', 404);
    }

    return ok({ success: true });
  } catch (error) {
    console.error('Failed to remove tag:', error);
    return bad(error instanceof Error ? error.message : 'Failed to remove tag', 500);
  }
});

// Embedding service endpoints

// Generate embedding for text
app.post('/api/embeddings/generate', async (c) => {
  try {
    const body = await c.req.json();
    const { text } = body;

    if (!text || typeof text !== 'string') {
      return bad('Text input is required');
    }

    const embedding = await embeddingService.generateEmbedding(text);
    return ok({
      embedding,
      dimensions: embeddingService.getDimensions(),
      provider: embeddingService.getCurrentProvider(),
    });
  } catch (error) {
    console.error('Failed to generate embedding:', error);
    return bad(error instanceof Error ? error.message : 'Failed to generate embedding', 500);
  }
});

// Get embedding service info
app.get('/api/embeddings/info', async (c) => {
  try {
    const info = {
      provider: embeddingService.getCurrentProvider(),
      dimensions: embeddingService.getDimensions(),
      cache_stats: embeddingService.getCacheStats(),
    };
    return ok(info);
  } catch (error) {
    console.error('Failed to get embedding info:', error);
    return bad(error instanceof Error ? error.message : 'Failed to get embedding info', 500);
  }
});

export { app as agentResultsRoutes };
