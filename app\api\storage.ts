import { promises as fs } from 'fs';
import path from 'path';
import { AppState, AppStateSchema } from './domain';

// Ensure data folder is at app/data when server cwd is app/
const DATA_DIR = path.join(process.cwd(), 'data');
const DATA_FILE = path.join(DATA_DIR, 'store.json');

async function ensureDir() {
  await fs.mkdir(DATA_DIR, { recursive: true });
}

export async function readState(): Promise<AppState> {
  await ensureDir();
  try {
    const raw = await fs.readFile(DATA_FILE, 'utf-8');
    const parsed = JSON.parse(raw);
    const state = AppStateSchema.parse(parsed);
    return state;
  } catch (e: any) {
    if (e?.code === 'ENOENT') {
      return { vendors: [], rfqs: [], quotes: [], reports: [] };
    }
    // Return empty state on parse error but do not lose the file
    return { vendors: [], rfqs: [], quotes: [], reports: [] };
  }
}

export async function writeState(next: AppState): Promise<void> {
  await ensureDir();
  const content = JSON.stringify(next, null, 2);
  await fs.writeFile(DATA_FILE, content, 'utf-8');
}

export async function updateState(mutator: (s: AppState) => AppState): Promise<AppState> {
  const s = await readState();
  const next = mutator(s);
  await writeState(next);
  return next;
}

