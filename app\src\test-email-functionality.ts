// Test script for email functionality
// This can be run in the browser console to test the email features

import { useRFQStore } from './store/rfqStore';
import type { RFQ, RFQLineItem } from './store/rfqStore';

// Test function to create a sample RFQ for testing email functionality
export function createTestRFQ(): RFQ {
  const store = useRFQStore.getState();
  
  const testLineItems: RFQLineItem[] = [
    {
      id: 'item-1',
      itemName: 'Commercial Bain Marie',
      partNumber: 'BM-4GN-150',
      quantity: 2,
      unit: 'units',
      materialSpec: 'Food-grade stainless steel (minimum AISI 304/SS304)',
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      notes: 'For professional kitchen use in Mangaluru, Karnataka. Must include GN pans and lids.'
    },
    {
      id: 'item-2',
      itemName: 'Digital Temperature Control Unit',
      partNumber: 'DTC-REL-2KW',
      quantity: 2,
      unit: 'units',
      materialSpec: 'Digital thermostat/temperature control with reliable sensors',
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      notes: 'Minimum 2kW, maximum 2.5kW capacity with overheat protection'
    }
  ];

  const testRFQ = store.createRFQ({
    id: 'test-rfq-' + Date.now(),
    refNumber: 'RFQ-TEST-001',
    title: 'Commercial Bain Marie for Professional Kitchen',
    description: 'We are seeking quotations for the supply, delivery, installation, and commissioning of a Commercial Bain Marie for use in a professional kitchen in Mangaluru, Karnataka, based on our recommendation report and best procurement practices.',
    client: {
      company: 'Mangaluru Catering Services',
      contactName: 'Procurement Manager',
      email: '<EMAIL>',
      phone: '+91-************',
      address: 'Industrial Area, Mangaluru, Karnataka 575001'
    },
    dueDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(), // 45 days from now
    responseDeadline: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days from now
    notes: 'All sections align with compliance, technical, ESG, and regional requirements. Please ensure CE/BIS/ISI certification for electrical safety.',
    attachments: [
      {
        id: 'att-1',
        name: 'Technical_Specifications.pdf',
        type: 'application/pdf'
      },
      {
        id: 'att-2',
        name: 'Installation_Requirements.docx',
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      }
    ],
    lineItems: testLineItems,
    invitedVendorIds: [],
    createdAt: new Date().toISOString(),
    status: 'Draft'
  });

  return testRFQ;
}

// Test function to create sample vendors
export function createTestVendors() {
  const store = useRFQStore.getState();
  
  const vendors = [
    {
      id: 'vendor-1',
      name: 'EcoPackage India',
      email: '<EMAIL>',
      phone: '+91-************',
      company: 'EcoPackage India Pvt Ltd',
      address: 'Mangaluru Industrial Estate, Karnataka',
      website: 'https://ecopackage.in',
      favorite: true,
      notes: 'Reliable supplier for kitchen equipment'
    },
    {
      id: 'vendor-2',
      name: 'Kitchen Solutions Ltd',
      email: '<EMAIL>',
      phone: '+91-************',
      company: 'Kitchen Solutions Limited',
      address: 'Commercial Complex, Mangaluru',
      website: 'https://kitchensolutions.com',
      favorite: false,
      notes: 'Specializes in commercial kitchen equipment'
    },
    {
      id: 'vendor-3',
      name: 'Tech Equipment Co',
      email: '<EMAIL>',
      phone: '+91-************',
      company: 'Tech Equipment Company',
      address: 'Industrial Zone, Mangaluru',
      favorite: false,
      notes: 'Good for electrical components and controls'
    }
  ];

  vendors.forEach(vendor => {
    store.addVendor(vendor);
  });

  return vendors;
}

// Test function to simulate email sending
export async function testEmailSending() {
  console.log('🧪 Testing Email Functionality');
  
  // Create test data
  const vendors = createTestVendors();
  const rfq = createTestRFQ();
  
  console.log('✅ Created test RFQ:', rfq.refNumber);
  console.log('✅ Created test vendors:', vendors.length);
  
  // Test email data
  const emailData = {
    rfqId: rfq.id,
    recipients: vendors.map(v => v.email).filter(Boolean),
    subject: `RFQ ${rfq.refNumber}: ${rfq.title}`,
    message: `Dear Vendor,

We are pleased to invite you to submit a quotation for the following requirements:

RFQ Number: ${rfq.refNumber}
Title: ${rfq.title}
Response Deadline: ${new Date(rfq.responseDeadline!).toLocaleDateString()}

${rfq.description}

Please review the attached RFQ document for detailed specifications and requirements.

We look forward to receiving your competitive quotation.

Best regards,
${rfq.client?.contactName}
${rfq.client?.company}`,
    includeAttachments: true
  };

  console.log('📧 Email data prepared:', {
    recipients: emailData.recipients.length,
    subject: emailData.subject.substring(0, 50) + '...',
    messageLength: emailData.message.length
  });

  try {
    // Test the email sending through the store
    const store = useRFQStore.getState();
    const emailRecord = await store.sendRFQEmail(emailData);
    
    console.log('✅ Email sent successfully:', emailRecord);
    return emailRecord;
    
  } catch (error) {
    console.error('❌ Email sending failed:', error);
    throw error;
  }
}

// Test function to check email configuration
export async function testEmailConfig() {
  try {
    const response = await fetch('/api/email/config');
    const config = await response.json();
    
    console.log('📧 Email configuration:', config);
    return config;
    
  } catch (error) {
    console.error('❌ Failed to check email config:', error);
    throw error;
  }
}

// Main test function
export async function runEmailTests() {
  console.log('🚀 Starting Email Functionality Tests');
  
  try {
    // Test 1: Check email configuration
    console.log('\n1️⃣ Testing email configuration...');
    await testEmailConfig();
    
    // Test 2: Test email sending
    console.log('\n2️⃣ Testing email sending...');
    await testEmailSending();
    
    console.log('\n✅ All email tests passed!');
    
  } catch (error) {
    console.error('\n❌ Email tests failed:', error);
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).emailTests = {
    createTestRFQ,
    createTestVendors,
    testEmailSending,
    testEmailConfig,
    runEmailTests
  };
  
  console.log('📧 Email test functions available at window.emailTests');
  console.log('Run window.emailTests.runEmailTests() to start testing');
}
