import { z } from 'zod';

// Agent execution status enum
export const AgentExecutionStatus = z.enum(['running', 'completed', 'failed', 'terminated']);
export type AgentExecutionStatus = z.infer<typeof AgentExecutionStatus>;

// Step execution status enum
export const StepExecutionStatus = z.enum(['pending', 'running', 'completed', 'failed', 'timeout']);
export type StepExecutionStatus = z.infer<typeof StepExecutionStatus>;

// Tag type enum
export const TagType = z.enum(['system', 'user', 'custom', 'auto']);
export type TagType = z.infer<typeof TagType>;

// Agent result schema
export const AgentResultSchema = z.object({
  id: z.string().uuid(),
  workflow_id: z.string(),
  
  // Input and execution metadata
  input_text: z.string(),
  input_hash: z.string(),
  
  // Execution status and timing
  status: AgentExecutionStatus,
  started_at: z.date(),
  completed_at: z.date().nullable(),
  execution_time_ms: z.number().nullable(),
  
  // Agent workflow results (JSON fields)
  query_analysis: z.any().nullable(),
  market_research: z.any().nullable(),
  recommendations: z.any().nullable(),
  documents: z.any().nullable(),
  action_plan: z.any().nullable(),
  rfq_data: z.any().nullable(),
  git_commit_info: z.any().nullable(),
  
  // Error handling
  error_message: z.string().nullable(),
  error_trace: z.any().nullable(),
  partial_results: z.boolean().default(false),
  
  // Workflow control metadata
  workflow_config: z.any().nullable(),
  workflow_steps: z.any().nullable(),
  total_steps: z.number().nullable(),
  completed_steps: z.number().nullable(),
  failed_steps: z.number().nullable(),
  
  // System metadata
  system_status: z.any().nullable(),
  region: z.string().default('Mangaluru, India'),
  agent_version: z.string().nullable(),
  
  // Embeddings (as arrays of numbers)
  input_embedding: z.array(z.number()).nullable(),
  output_embedding: z.array(z.number()).nullable(),
  combined_embedding: z.array(z.number()).nullable(),
  
  // Audit fields
  created_at: z.date(),
  updated_at: z.date(),
});

export type AgentResult = z.infer<typeof AgentResultSchema>;

// Agent execution step schema
export const AgentExecutionStepSchema = z.object({
  id: z.string().uuid(),
  result_id: z.string().uuid(),
  workflow_id: z.string(),
  
  // Step identification
  step_id: z.string(),
  step_name: z.string(),
  step_order: z.number(),
  
  // Step execution details
  status: StepExecutionStatus,
  started_at: z.date().nullable(),
  completed_at: z.date().nullable(),
  duration_ms: z.number().nullable(),
  attempts: z.number().default(1),
  
  // Step results and errors
  result_data: z.any().nullable(),
  error_message: z.string().nullable(),
  error_details: z.any().nullable(),
  
  // Audit fields
  created_at: z.date(),
  updated_at: z.date(),
});

export type AgentExecutionStep = z.infer<typeof AgentExecutionStepSchema>;

// Agent result tag schema
export const AgentResultTagSchema = z.object({
  id: z.string().uuid(),
  result_id: z.string().uuid(),
  tag_name: z.string(),
  tag_value: z.string().nullable(),
  tag_type: TagType.default('custom'),
  created_at: z.date(),
});

export type AgentResultTag = z.infer<typeof AgentResultTagSchema>;

// Input schemas for creating/updating records
export const CreateAgentResultSchema = z.object({
  workflow_id: z.string(),
  input_text: z.string(),
  input_hash: z.string(),
  status: AgentExecutionStatus.default('running'),
  
  // Optional fields
  query_analysis: z.any().optional(),
  market_research: z.any().optional(),
  recommendations: z.any().optional(),
  documents: z.any().optional(),
  action_plan: z.any().optional(),
  rfq_data: z.any().optional(),
  git_commit_info: z.any().optional(),
  
  error_message: z.string().optional(),
  error_trace: z.any().optional(),
  partial_results: z.boolean().default(false),
  
  workflow_config: z.any().optional(),
  workflow_steps: z.any().optional(),
  total_steps: z.number().optional(),
  completed_steps: z.number().optional(),
  failed_steps: z.number().optional(),
  
  system_status: z.any().optional(),
  region: z.string().default('Mangaluru, India'),
  agent_version: z.string().optional(),
  
  input_embedding: z.array(z.number()).optional(),
  output_embedding: z.array(z.number()).optional(),
  combined_embedding: z.array(z.number()).optional(),
});

export type CreateAgentResult = z.infer<typeof CreateAgentResultSchema>;

export const UpdateAgentResultSchema = CreateAgentResultSchema.partial().extend({
  completed_at: z.date().optional(),
  execution_time_ms: z.number().optional(),
});

export type UpdateAgentResult = z.infer<typeof UpdateAgentResultSchema>;

export const CreateAgentExecutionStepSchema = z.object({
  result_id: z.string().uuid(),
  workflow_id: z.string(),
  step_id: z.string(),
  step_name: z.string(),
  step_order: z.number(),
  status: StepExecutionStatus.default('pending'),
  
  started_at: z.date().optional(),
  completed_at: z.date().optional(),
  duration_ms: z.number().optional(),
  attempts: z.number().default(1),
  
  result_data: z.any().optional(),
  error_message: z.string().optional(),
  error_details: z.any().optional(),
});

export type CreateAgentExecutionStep = z.infer<typeof CreateAgentExecutionStepSchema>;

export const UpdateAgentExecutionStepSchema = CreateAgentExecutionStepSchema.partial();
export type UpdateAgentExecutionStep = z.infer<typeof UpdateAgentExecutionStepSchema>;

export const CreateAgentResultTagSchema = z.object({
  result_id: z.string().uuid(),
  tag_name: z.string(),
  tag_value: z.string().optional(),
  tag_type: TagType.default('custom'),
});

export type CreateAgentResultTag = z.infer<typeof CreateAgentResultTagSchema>;

// Search and query schemas
export const AgentResultSearchSchema = z.object({
  query: z.string().optional(),
  status: AgentExecutionStatus.optional(),
  region: z.string().optional(),
  start_date: z.date().optional(),
  end_date: z.date().optional(),
  tags: z.array(z.string()).optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
  sort_by: z.enum(['created_at', 'started_at', 'completed_at', 'execution_time_ms']).default('started_at'),
  sort_order: z.enum(['asc', 'desc']).default('desc'),
});

export type AgentResultSearch = z.infer<typeof AgentResultSearchSchema>;

export const VectorSearchSchema = z.object({
  embedding: z.array(z.number()).length(1536),
  embedding_type: z.enum(['input', 'output', 'combined']).default('combined'),
  similarity_threshold: z.number().min(0).max(1).default(0.7),
  limit: z.number().min(1).max(50).default(10),
  include_metadata: z.boolean().default(true),
});

export type VectorSearch = z.infer<typeof VectorSearchSchema>;

// Response schemas
export const AgentResultListResponseSchema = z.object({
  results: z.array(AgentResultSchema),
  total: z.number(),
  limit: z.number(),
  offset: z.number(),
  has_more: z.boolean(),
});

export type AgentResultListResponse = z.infer<typeof AgentResultListResponseSchema>;

export const VectorSearchResponseSchema = z.object({
  results: z.array(AgentResultSchema.extend({
    similarity_score: z.number(),
  })),
  query_embedding: z.array(z.number()),
  total_found: z.number(),
});

export type VectorSearchResponse = z.infer<typeof VectorSearchResponseSchema>;
