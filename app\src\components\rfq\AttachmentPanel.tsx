import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useRef } from "react";
import type { Attachment } from "@/store/rfqStore";

export function AttachmentPanel({ items, onAdd, onRemove }: { items: Attachment[]; onAdd: (file: Omit<Attachment, "id">) => void; onRemove: (id: string) => void }) {
  const inputRef = useRef<HTMLInputElement>(null);
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Attachments</CardTitle>
        <Button variant="outline" onClick={() => inputRef.current?.click()}>Upload</Button>
        <input ref={inputRef} className="hidden" type="file" onChange={(e) => {
          const f = e.target.files?.[0];
          if (!f) return;
          const url = URL.createObjectURL(f);
          onAdd({ name: f.name, url, type: f.type });
          e.currentTarget.value = "";
        }} />
      </CardHeader>
      <CardContent>
        <ul className="space-y-2">
          {items.map(a => (
            <li key={a.id} className="flex items-center justify-between border rounded p-2">
              <span className="truncate">{a.name}</span>
              <div className="flex items-center gap-2">
                {a.url && <a className="text-sm underline" href={a.url} target="_blank">View</a>}
                <Button variant="ghost" size="sm" onClick={() => onRemove(a.id)}>Remove</Button>
              </div>
            </li>
          ))}
          {items.length === 0 && <p className="text-sm text-muted-foreground">No attachments</p>}
        </ul>
      </CardContent>
    </Card>
  );
}

