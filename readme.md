# Enhanced Procurement Agent 🚀

An advanced AI-powered procurement agent with automated RFQ generation, GitHub integration, and robust workflow control. This project resolves infinite loop issues and implements comprehensive automation for procurement workflows.

## ✨ Key Features

- **🔄 Deterministic Workflow Control**: Prevents infinite loops with timeout mechanisms and maximum iteration safeguards
- **📋 Automated RFQ Generation**: Mandatory RFQ creation integrated directly into the workflow
- **🔗 GitHub Integration**: Automated commit and push of workflow results
- **📊 Comprehensive Logging**: Detailed monitoring and error tracking
- **🛡️ Robust Error Handling**: Graceful degradation and recovery mechanisms
- **⚡ Performance Optimized**: Efficient resource management and timeout controls

## 🎯 Problem Solved

This enhanced version addresses critical issues in the original procurement agent:

1. **Infinite Loop Resolution**: Implemented deterministic exit conditions and maximum iteration limits
2. **Automated RFQ Creation**: RFQ generation is now mandatory and integrated into the main workflow
3. **Version Control Automation**: Automatic staging, committing, and pushing of results to GitHub
4. **Enhanced Monitoring**: Comprehensive logging and system status tracking
5. **Definitive Completion**: Every workflow returns a definitive result with explicit completion status

## 🏗️ Enhanced Architecture

```
├── agent.ts                      # Enhanced workflow with control flow
├── services/                     # Core services
│   ├── rfqGenerationService.ts   # Automated RFQ creation
│   ├── githubIntegrationService.ts # Git operations
│   └── loggingService.ts         # Comprehensive logging
├── tests/                        # Integration tests
│   └── integration-tests.js      # Full workflow validation
├── validate-workflow.js          # Standalone validation
├── workflow-outputs/             # Generated workflow results
├── logs/                         # System logs and metrics
└── app/                          # React-based UI
    ├── api/                      # API endpoints
    ├── src/                      # Components and utilities
    └── ...                       # Standard React app structure
```

## 🔧 Core Services

### WorkflowController
- **Timeout Management**: 45s per step, 10min total workflow timeout
- **Retry Logic**: 2 retry attempts with exponential backoff
- **Step Tracking**: Detailed monitoring of each workflow step
- **Termination Control**: Deterministic exit conditions

### RFQGenerationService
- **Automated Creation**: Mandatory RFQ generation from analysis data
- **Data Validation**: Zod schema validation for RFQ structure
- **Smart Extraction**: AI-powered extraction of line items and specifications
- **Storage Integration**: Automatic saving to RFQ management system

### GitHubIntegrationService
- **Automated Commits**: Stage and commit workflow results
- **Secure Push**: Token-based authentication for remote operations
- **File Management**: Organized storage of workflow outputs
- **Error Recovery**: Graceful handling of git operation failures

### LoggingService
- **Multi-Level Logging**: DEBUG, INFO, WARN, ERROR, CRITICAL levels
- **Workflow Tracking**: Complete audit trail of workflow execution
- **Performance Metrics**: Step-by-step timing and success rates
- **File & Console Output**: Dual logging for development and production

## 📋 Prerequisites

- **Node.js 21+**: Required for modern ES modules and TypeScript support
- **Git**: For version control integration
- **API Keys**: Perplexity AI and optional GitHub token
- **Package Manager**: npm or pnpm

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pnpm install
```

### 2. Environment Configuration
```bash
cp .env.example .env
```

### 3. Configure API Keys
Add the following to your `.env` file:

```env
# Required: Perplexity AI API key for market research
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# Optional: GitHub token for automated commits (recommended)
GITHUB_TOKEN=your_github_token_here

# Optional: Git configuration
GIT_USER_EMAIL=<EMAIL>
GIT_USER_NAME=Your Name
GITHUB_REPO_URL=https://github.com/yourusername/yourrepo.git

# Optional: Langbase integration
LANGBASE_API_KEY=your_langbase_api_key_here
LANGBASE_API_URL=https://api.langbase.com/your-endpoint

# Optional: Agent mode (local/hosted)
AGENT_MODE=local
```

### 4. Validate Installation
```bash
npm run validate
```

This will run a comprehensive test of the enhanced workflow including:
- ✅ Workflow termination control
- ✅ RFQ generation
- ✅ GitHub integration
- ✅ Logging system
- ✅ Error handling

## Usage

### Running the Agent

Execute the agent with:

```bash
pnpm run agent
```

This will run the agent with the default test input defined in [`agent.ts`](agent.ts).

### Customizing Input

To test with your own text, modify the input in the IIFE at the bottom of [`agent.ts`](agent.ts):

```typescript
const event = {
	json: async () => ({
		input: `Your custom text to summarize goes here.`
	})
};
```

### Integration

The agent exports a default function that can be integrated into other applications:

```typescript
import agent from './agent.ts';

const event = {
	json: async () => ({ input: 'Text to summarize' })
};

const result = await agent(event, {});
console.log(result.summary);
```

## Agent App

The project includes a React-based agent app in the [`app/`](app/) directory with:

- UI components built with modern React patterns
- API integration for agent communication
- Responsive design with Tailwind CSS
- Development server with Vite and Hono

### Installation

1. Install dependencies:

To set up the app, navigate to the [`app/`](app/) directory and install dependencies:

```bash
cd app
pnpm install
```

2. Set up environment variables:

```bash
cp .env.example .env
```

3. Add your API keys to `.env`:

```env
# Get API key from: https://command.new/aymnsh174883/perplex-procure-agent-4b33
LANGBASE_API_KEY=your_langbase_api_key_here

# Your OpenAI API key: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Add any other required environment variables
```

### Usage

To run the application, use:

```bash
pnpm dev
```

## Support

For questions or issues, please refer to the [Langbase documentation](https://langbase.com/docs).
