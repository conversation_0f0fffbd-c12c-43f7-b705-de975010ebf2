#!/usr/bin/env node

import dotenv from 'dotenv';
import { DatabaseMigrator } from '../api/database/migrate.js';
import { testConnection, checkPgVectorExtension } from '../api/database/config.js';

// Load environment variables
dotenv.config();

async function setupDatabase() {
  console.log('🚀 Setting up PostgreSQL database with pgvector for Agent Results...');
  console.log('================================================================');

  try {
    // Check environment variables
    console.log('🔍 Checking environment configuration...');
    
    const requiredEnvVars = [
      'DATABASE_HOST',
      'DATABASE_PORT', 
      'DATABASE_NAME',
      'DATABASE_USER',
      'DATABASE_PASSWORD'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      console.error('❌ Missing required environment variables:');
      missingVars.forEach(varName => console.error(`   - ${varName}`));
      console.log('\n💡 Please set these variables in your .env file:');
      console.log('   DATABASE_HOST=localhost');
      console.log('   DATABASE_PORT=5432');
      console.log('   DATABASE_NAME=procure_agent');
      console.log('   DATABASE_USER=your_username');
      console.log('   DATABASE_PASSWORD=your_password');
      process.exit(1);
    }

    console.log('✅ Environment variables configured');

    // Test database connection
    console.log('\n🔌 Testing database connection...');
    const isConnected = await testConnection();
    
    if (!isConnected) {
      console.error('❌ Database connection failed');
      console.log('\n💡 Troubleshooting tips:');
      console.log('   1. Ensure PostgreSQL is running');
      console.log('   2. Check your database credentials in .env');
      console.log('   3. Verify the database exists');
      console.log('   4. Check firewall/network settings');
      process.exit(1);
    }

    console.log('✅ Database connection successful');

    // Check pgvector extension
    console.log('\n🧠 Checking pgvector extension...');
    const hasVector = await checkPgVectorExtension();
    
    if (!hasVector) {
      console.log('⚠️ pgvector extension not found');
      console.log('\n💡 Installing pgvector extension...');
      console.log('   If this fails, you may need to install pgvector manually:');
      console.log('   - Ubuntu/Debian: apt install postgresql-15-pgvector');
      console.log('   - macOS: brew install pgvector');
      console.log('   - From source: https://github.com/pgvector/pgvector#installation');
    } else {
      console.log('✅ pgvector extension available');
    }

    // Run migrations
    console.log('\n📦 Running database migrations...');
    const migrator = new DatabaseMigrator();
    await migrator.runMigrations();

    // Show database info
    console.log('\n📊 Database setup completed! Here\'s the current state:');
    await migrator.getTableInfo();

    // Offer to seed test data
    console.log('\n🌱 Would you like to add test data? (Recommended for development)');
    console.log('   This will create sample agent results for testing the system.');
    
    // For now, automatically seed in setup
    console.log('   Adding test data...');
    await migrator.seedTestData();

    console.log('\n🎉 Database setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Start your application server');
    console.log('   2. Test the agent workflow - results will now be persisted');
    console.log('   3. Use the API endpoints to search and retrieve results');
    console.log('   4. Check the database for stored results and embeddings');

    console.log('\n🔗 Useful commands:');
    console.log('   - View database info: npm run db:info');
    console.log('   - Reset database: npm run db:reset');
    console.log('   - Add test data: npm run db:seed');

  } catch (error) {
    console.error('\n❌ Database setup failed:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('connect')) {
        console.log('\n💡 Connection troubleshooting:');
        console.log('   - Is PostgreSQL running?');
        console.log('   - Are your credentials correct?');
        console.log('   - Does the database exist?');
      } else if (error.message.includes('pgvector')) {
        console.log('\n💡 pgvector troubleshooting:');
        console.log('   - Install pgvector extension for PostgreSQL');
        console.log('   - Ensure you have superuser privileges');
        console.log('   - Check PostgreSQL version compatibility');
      }
    }
    
    process.exit(1);
  }
}

// Run setup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  setupDatabase().catch(console.error);
}

export { setupDatabase };
