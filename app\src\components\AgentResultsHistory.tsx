import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from './ui/tabs';
import { ScrollArea } from './ui/scroll-area';
import { Separator } from './ui/separator';
import { Search, Clock, CheckCircle, XCircle, AlertCircle, Database, Zap } from 'lucide-react';
import { toast } from 'sonner';

interface AgentResult {
  id: string;
  workflow_id: string;
  input_text: string;
  status: 'running' | 'completed' | 'failed' | 'terminated';
  started_at: string;
  completed_at?: string;
  execution_time_ms?: number;
  query_analysis?: any;
  recommendations?: any;
  documents?: any;
  action_plan?: any;
  rfq_data?: any;
  error_message?: string;
  region: string;
  agent_version?: string;
  created_at: string;
  similarity_score?: number;
}

interface AgentResultsStats {
  total: number;
  by_status: Record<string, number>;
  recent_24h: number;
  avg_execution_time_ms: number;
}

export function AgentResultsHistory() {
  const [results, setResults] = useState<AgentResult[]>([]);
  const [stats, setStats] = useState<AgentResultsStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedResult, setSelectedResult] = useState<AgentResult | null>(null);
  const [searchType, setSearchType] = useState<'traditional' | 'semantic'>('traditional');

  // Fetch recent results
  const fetchRecentResults = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/agent-results/recent?limit=20');
      if (response.ok) {
        const data = await response.json();
        setResults(data);
      } else {
        toast.error('Failed to fetch recent results');
      }
    } catch (error) {
      console.error('Error fetching recent results:', error);
      toast.error('Error fetching recent results');
    } finally {
      setLoading(false);
    }
  };

  // Fetch statistics
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/agent-results/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  // Search results
  const searchResults = async () => {
    if (!searchQuery.trim()) {
      fetchRecentResults();
      return;
    }

    setLoading(true);
    try {
      let response;
      
      if (searchType === 'semantic') {
        // Semantic search using vector similarity
        response = await fetch('/api/agent-results/find-similar', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            text: searchQuery,
            limit: 20,
            threshold: 0.6
          })
        });
      } else {
        // Traditional text search
        const params = new URLSearchParams({
          query: searchQuery,
          limit: '20'
        });
        response = await fetch(`/api/agent-results?${params}`);
      }

      if (response.ok) {
        const data = await response.json();
        setResults(searchType === 'semantic' ? data.results : data.results);
      } else {
        toast.error('Search failed');
      }
    } catch (error) {
      console.error('Error searching results:', error);
      toast.error('Search error');
    } finally {
      setLoading(false);
    }
  };

  // Format execution time
  const formatExecutionTime = (ms?: number) => {
    if (!ms) return 'N/A';
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  // Get status icon and color
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'completed':
        return { icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-100' };
      case 'failed':
        return { icon: XCircle, color: 'text-red-600', bg: 'bg-red-100' };
      case 'running':
        return { icon: Clock, color: 'text-blue-600', bg: 'bg-blue-100' };
      case 'terminated':
        return { icon: AlertCircle, color: 'text-orange-600', bg: 'bg-orange-100' };
      default:
        return { icon: Clock, color: 'text-gray-600', bg: 'bg-gray-100' };
    }
  };

  useEffect(() => {
    fetchRecentResults();
    fetchStats();
  }, []);

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Database className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-sm font-medium">Total Results</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-green-600" />
                <div>
                  <p className="text-sm font-medium">Recent (24h)</p>
                  <p className="text-2xl font-bold">{stats.recent_24h}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <div>
                  <p className="text-sm font-medium">Completed</p>
                  <p className="text-2xl font-bold">{stats.by_status.completed || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Zap className="h-4 w-4 text-purple-600" />
                <div>
                  <p className="text-sm font-medium">Avg Time</p>
                  <p className="text-2xl font-bold">{formatExecutionTime(stats.avg_execution_time_ms)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Search Interface */}
      <Card>
        <CardHeader>
          <CardTitle>Search Agent Results</CardTitle>
          <CardDescription>
            Search through historical agent executions using traditional text search or semantic similarity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Tabs value={searchType} onValueChange={(value) => setSearchType(value as any)}>
              <TabsList>
                <TabsTrigger value="traditional">Text Search</TabsTrigger>
                <TabsTrigger value="semantic">Semantic Search</TabsTrigger>
              </TabsList>
            </Tabs>
            
            <div className="flex space-x-2">
              <Input
                placeholder={searchType === 'semantic' 
                  ? "Find similar workflows (e.g., 'industrial pumps procurement')" 
                  : "Search by keywords, status, or content"
                }
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && searchResults()}
              />
              <Button onClick={searchResults} disabled={loading}>
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" onClick={fetchRecentResults}>
                Recent
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Results ({results.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[600px]">
              <div className="space-y-3">
                {loading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-sm text-gray-600">Loading results...</p>
                  </div>
                ) : results.length === 0 ? (
                  <div className="text-center py-8">
                    <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">No results found</p>
                  </div>
                ) : (
                  results.map((result) => {
                    const statusDisplay = getStatusDisplay(result.status);
                    const StatusIcon = statusDisplay.icon;
                    
                    return (
                      <Card 
                        key={result.id} 
                        className={`cursor-pointer transition-colors hover:bg-gray-50 ${
                          selectedResult?.id === result.id ? 'ring-2 ring-blue-500' : ''
                        }`}
                        onClick={() => setSelectedResult(result)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2 mb-2">
                                <StatusIcon className={`h-4 w-4 ${statusDisplay.color}`} />
                                <Badge variant="secondary" className={statusDisplay.bg}>
                                  {result.status}
                                </Badge>
                                {result.similarity_score && (
                                  <Badge variant="outline">
                                    {(result.similarity_score * 100).toFixed(1)}% match
                                  </Badge>
                                )}
                              </div>
                              
                              <p className="text-sm font-medium truncate mb-1">
                                {result.input_text}
                              </p>
                              
                              <div className="flex items-center space-x-4 text-xs text-gray-500">
                                <span>{new Date(result.started_at).toLocaleDateString()}</span>
                                <span>{formatExecutionTime(result.execution_time_ms)}</span>
                                <span>{result.region}</span>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })
                )}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Result Details */}
        <Card>
          <CardHeader>
            <CardTitle>Result Details</CardTitle>
          </CardHeader>
          <CardContent>
            {selectedResult ? (
              <ScrollArea className="h-[600px]">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Input Query</h4>
                    <p className="text-sm bg-gray-50 p-3 rounded">{selectedResult.input_text}</p>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-2">Execution Info</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Status:</span>
                        <Badge className="ml-2">{selectedResult.status}</Badge>
                      </div>
                      <div>
                        <span className="text-gray-600">Duration:</span>
                        <span className="ml-2">{formatExecutionTime(selectedResult.execution_time_ms)}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Started:</span>
                        <span className="ml-2">{new Date(selectedResult.started_at).toLocaleString()}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Region:</span>
                        <span className="ml-2">{selectedResult.region}</span>
                      </div>
                    </div>
                  </div>

                  {selectedResult.error_message && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="font-medium mb-2 text-red-600">Error</h4>
                        <p className="text-sm bg-red-50 p-3 rounded text-red-800">
                          {selectedResult.error_message}
                        </p>
                      </div>
                    </>
                  )}

                  {selectedResult.query_analysis && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="font-medium mb-2">Query Analysis</h4>
                        <pre className="text-xs bg-gray-50 p-3 rounded overflow-auto">
                          {typeof selectedResult.query_analysis === 'string' 
                            ? selectedResult.query_analysis 
                            : JSON.stringify(selectedResult.query_analysis, null, 2)
                          }
                        </pre>
                      </div>
                    </>
                  )}

                  {selectedResult.recommendations && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="font-medium mb-2">Recommendations</h4>
                        <pre className="text-xs bg-gray-50 p-3 rounded overflow-auto">
                          {typeof selectedResult.recommendations === 'string' 
                            ? selectedResult.recommendations 
                            : JSON.stringify(selectedResult.recommendations, null, 2)
                          }
                        </pre>
                      </div>
                    </>
                  )}

                  {selectedResult.rfq_data && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="font-medium mb-2">RFQ Data</h4>
                        <pre className="text-xs bg-gray-50 p-3 rounded overflow-auto">
                          {JSON.stringify(selectedResult.rfq_data, null, 2)}
                        </pre>
                      </div>
                    </>
                  )}
                </div>
              </ScrollArea>
            ) : (
              <div className="text-center py-8">
                <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Select a result to view details</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
