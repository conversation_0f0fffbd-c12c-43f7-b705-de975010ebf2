// Test script for LangGraph + Perplexity integration
import fetch from 'node-fetch';

async function testProcurementAgent() {
  console.log('Testing Procurement Agent with LangGraph + Perplexity...\n');
  
  const testCases = [
    {
      name: 'Sustainable Packaging Suppliers',
      input: 'Find sustainable packaging suppliers in Mangaluru under $5,000 budget with ESG compliance'
    },
    {
      name: 'Office Equipment RFQ',
      input: 'Create RFQ for office furniture including desks, chairs, and storage solutions for 50 employees'
    },
    {
      name: 'IT Hardware Procurement',
      input: 'Generate procurement recommendations for laptops and networking equipment for a small business'
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n=== Testing: ${testCase.name} ===`);
    console.log(`Input: ${testCase.input}\n`);
    
    try {
      const response = await fetch('http://localhost:3101/api/agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ input: testCase.input })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      console.log('Status:', result.status);
      console.log('Source:', result.source || 'N/A');
      
      if (result.query_analysis) {
        console.log('\n📊 Query Analysis:');
        console.log(result.query_analysis.substring(0, 200) + '...');
      }
      
      if (result.market_intelligence) {
        console.log('\n🔍 Market Intelligence:');
        console.log(result.market_intelligence.substring(0, 200) + '...');
      }
      
      if (result.recommendations) {
        console.log('\n💡 Recommendations:');
        console.log(result.recommendations.substring(0, 200) + '...');
      }
      
      if (result.documents) {
        console.log('\n📄 Generated Documents:');
        console.log(result.documents.substring(0, 200) + '...');
      }
      
      if (result.action_plan) {
        console.log('\n📋 Action Plan:');
        console.log(result.action_plan.substring(0, 200) + '...');
      }
      
      console.log('\n✅ Test completed successfully!');
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
      
      if (error.message.includes('ECONNREFUSED')) {
        console.log('💡 Make sure the server is running on http://localhost:3101');
      }
    }
    
    console.log('\n' + '='.repeat(60));
  }
}

// Run the tests
testProcurementAgent().catch(console.error);
