#!/usr/bin/env node

/**
 * Comprehensive Integration Tests for Enhanced Procurement Agent
 * Tests workflow termination, RFQ creation, GitHub integration, and logging
 */

import { execSync } from 'child_process';
import { existsSync, readFileSync, rmSync } from 'fs';
import { join } from 'path';

// Test configuration
const TEST_CONFIG = {
  serverUrl: 'http://localhost:3101',
  testTimeout: 120000, // 2 minutes per test
  logDir: 'logs',
  outputDir: 'workflow-outputs'
};

// Test cases
const TEST_CASES = [
  {
    name: 'Basic Procurement Request',
    input: 'I need to procure 100 laptops for our office in Mangaluru',
    expectedSteps: ['query-analysis', 'market-research', 'recommendations', 'documents', 'action-plan', 'rfq-generation', 'git-commit-push'],
    shouldSucceed: true
  },
  {
    name: 'Complex Procurement with Specifications',
    input: 'Need industrial equipment: 5 CNC machines, 3 welding stations, safety compliance required, budget 50 lakhs, delivery in Mangaluru',
    expectedSteps: ['query-analysis', 'market-research', 'recommendations', 'documents', 'action-plan', 'rfq-generation', 'git-commit-push'],
    shouldSucceed: true
  },
  {
    name: 'Invalid Input Test',
    input: '',
    expectedSteps: [],
    shouldSucceed: false
  },
  {
    name: 'Timeout Test (Long Input)',
    input: 'A'.repeat(10000) + ' - procurement request with extremely long description that might cause timeout issues',
    expectedSteps: ['query-analysis'],
    shouldSucceed: true // Should handle gracefully
  }
];

class IntegrationTester {
  constructor() {
    this.results = [];
    this.startTime = Date.now();
  }

  async runAllTests() {
    console.log('🧪 Starting Enhanced Procurement Agent Integration Tests');
    console.log('=' .repeat(80));

    // Pre-test setup
    await this.setupTests();

    // Run individual tests
    for (const testCase of TEST_CASES) {
      await this.runTest(testCase);
    }

    // Post-test validation
    await this.validateSystemState();

    // Generate report
    this.generateReport();
  }

  async setupTests() {
    console.log('🔧 Setting up tests...');
    
    // Clean up previous test artifacts
    if (existsSync(TEST_CONFIG.logDir)) {
      console.log('🧹 Cleaning up previous logs...');
      rmSync(TEST_CONFIG.logDir, { recursive: true, force: true });
    }

    if (existsSync(TEST_CONFIG.outputDir)) {
      console.log('🧹 Cleaning up previous outputs...');
      rmSync(TEST_CONFIG.outputDir, { recursive: true, force: true });
    }

    // Check if server is running
    try {
      const response = await fetch(`${TEST_CONFIG.serverUrl}/health`);
      if (!response.ok) {
        throw new Error('Health check failed');
      }
      console.log('✅ Server is running and healthy');
    } catch (error) {
      console.log('⚠️ Server health check failed, attempting to start server...');
      // Note: In a real scenario, you might want to start the server here
      console.log('💡 Make sure the server is running on', TEST_CONFIG.serverUrl);
    }
  }

  async runTest(testCase) {
    console.log(`\n🧪 Running test: ${testCase.name}`);
    console.log('-'.repeat(50));

    const testStartTime = Date.now();
    const testResult = {
      name: testCase.name,
      startTime: testStartTime,
      endTime: null,
      duration: null,
      success: false,
      error: null,
      response: null,
      validations: []
    };

    try {
      // Make request to agent
      console.log(`📤 Sending request: "${testCase.input.substring(0, 100)}${testCase.input.length > 100 ? '...' : ''}"`);
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), TEST_CONFIG.testTimeout);

      const response = await fetch(`${TEST_CONFIG.serverUrl}/api/agent`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ input: testCase.input }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      testResult.response = result;

      console.log(`📥 Response status: ${result.status}`);
      console.log(`🆔 Workflow ID: ${result.workflow_id || 'N/A'}`);

      // Validate response structure
      await this.validateResponse(testCase, result, testResult);

      // Validate workflow steps
      await this.validateWorkflowSteps(testCase, result, testResult);

      // Validate RFQ generation
      await this.validateRFQGeneration(testCase, result, testResult);

      // Validate GitHub integration
      await this.validateGitHubIntegration(testCase, result, testResult);

      // Validate logging
      await this.validateLogging(testCase, result, testResult);

      testResult.success = testCase.shouldSucceed ? 
        testResult.validations.every(v => v.passed) : 
        result.status === 'error';

      if (testResult.success) {
        console.log('✅ Test passed');
      } else {
        console.log('❌ Test failed');
        const failedValidations = testResult.validations.filter(v => !v.passed);
        failedValidations.forEach(v => console.log(`  - ${v.name}: ${v.error}`));
      }

    } catch (error) {
      testResult.error = error.message;
      testResult.success = !testCase.shouldSucceed; // If we expect failure, error is success
      
      if (error.name === 'AbortError') {
        console.log('⏰ Test timed out');
      } else {
        console.log(`❌ Test error: ${error.message}`);
      }
    } finally {
      testResult.endTime = Date.now();
      testResult.duration = testResult.endTime - testResult.startTime;
      this.results.push(testResult);
      
      console.log(`⏱️ Test duration: ${testResult.duration}ms`);
    }
  }

  async validateResponse(testCase, result, testResult) {
    const validations = [];

    // Basic structure validation
    validations.push({
      name: 'Response has status',
      passed: typeof result.status === 'string',
      error: typeof result.status !== 'string' ? 'Missing or invalid status' : null
    });

    validations.push({
      name: 'Response has timestamp',
      passed: typeof result.timestamp === 'string',
      error: typeof result.timestamp !== 'string' ? 'Missing or invalid timestamp' : null
    });

    if (testCase.shouldSucceed) {
      validations.push({
        name: 'Success status',
        passed: result.status === 'success',
        error: result.status !== 'success' ? `Expected success, got ${result.status}` : null
      });

      validations.push({
        name: 'Has workflow ID',
        passed: typeof result.workflow_id === 'string',
        error: typeof result.workflow_id !== 'string' ? 'Missing workflow ID' : null
      });

      validations.push({
        name: 'Has execution time',
        passed: typeof result.execution_time_ms === 'number' && result.execution_time_ms > 0,
        error: 'Missing or invalid execution time' 
      });
    }

    testResult.validations.push(...validations);
  }

  async validateWorkflowSteps(testCase, result, testResult) {
    if (!testCase.shouldSucceed) return;

    const validations = [];
    const workflowStatus = result.workflow_status;

    if (workflowStatus) {
      validations.push({
        name: 'Workflow completed',
        passed: workflowStatus.isComplete,
        error: !workflowStatus.isComplete ? 'Workflow not completed' : null
      });

      validations.push({
        name: 'No failed steps',
        passed: workflowStatus.failedSteps === 0,
        error: workflowStatus.failedSteps > 0 ? `${workflowStatus.failedSteps} steps failed` : null
      });

      validations.push({
        name: 'Expected steps completed',
        passed: workflowStatus.completedSteps >= testCase.expectedSteps.length,
        error: workflowStatus.completedSteps < testCase.expectedSteps.length ? 
          `Expected ${testCase.expectedSteps.length} steps, got ${workflowStatus.completedSteps}` : null
      });

      // Validate specific steps
      if (workflowStatus.steps) {
        for (const expectedStep of testCase.expectedSteps) {
          const step = workflowStatus.steps.find(s => s.id === expectedStep);
          validations.push({
            name: `Step ${expectedStep} completed`,
            passed: step && step.status === 'completed',
            error: !step ? `Step ${expectedStep} not found` : 
                   step.status !== 'completed' ? `Step ${expectedStep} status: ${step.status}` : null
          });
        }
      }
    }

    testResult.validations.push(...validations);
  }

  async validateRFQGeneration(testCase, result, testResult) {
    if (!testCase.shouldSucceed) return;

    const validations = [];

    validations.push({
      name: 'RFQ data present',
      passed: result.rfq_data && typeof result.rfq_data === 'object',
      error: !result.rfq_data ? 'RFQ data missing' : null
    });

    if (result.rfq_data) {
      const rfq = result.rfq_data;
      
      validations.push({
        name: 'RFQ has ID',
        passed: typeof rfq.id === 'string',
        error: typeof rfq.id !== 'string' ? 'RFQ missing ID' : null
      });

      validations.push({
        name: 'RFQ has title',
        passed: typeof rfq.title === 'string' && rfq.title.length > 0,
        error: 'RFQ missing or empty title'
      });

      validations.push({
        name: 'RFQ has line items',
        passed: Array.isArray(rfq.lineItems) && rfq.lineItems.length > 0,
        error: 'RFQ missing line items'
      });
    }

    testResult.validations.push(...validations);
  }

  async validateGitHubIntegration(testCase, result, testResult) {
    if (!testCase.shouldSucceed) return;

    const validations = [];

    validations.push({
      name: 'Git commit info present',
      passed: result.git_commit_info && typeof result.git_commit_info === 'object',
      error: !result.git_commit_info ? 'Git commit info missing' : null
    });

    if (result.git_commit_info) {
      const gitInfo = result.git_commit_info;
      
      validations.push({
        name: 'Git operation attempted',
        passed: typeof gitInfo.success === 'boolean',
        error: typeof gitInfo.success !== 'boolean' ? 'Git success status missing' : null
      });

      if (gitInfo.success) {
        validations.push({
          name: 'Commit SHA present',
          passed: typeof gitInfo.commitSha === 'string',
          error: 'Commit SHA missing'
        });

        validations.push({
          name: 'Files changed',
          passed: Array.isArray(gitInfo.filesChanged) && gitInfo.filesChanged.length > 0,
          error: 'No files were changed'
        });
      }
    }

    testResult.validations.push(...validations);
  }

  async validateLogging(testCase, result, testResult) {
    const validations = [];

    // Check if log files were created
    if (existsSync(TEST_CONFIG.logDir)) {
      const logFiles = execSync(`find ${TEST_CONFIG.logDir} -name "*.log" -type f`, { encoding: 'utf8' }).trim().split('\n').filter(Boolean);
      
      validations.push({
        name: 'Log files created',
        passed: logFiles.length > 0,
        error: logFiles.length === 0 ? 'No log files found' : null
      });

      if (logFiles.length > 0) {
        // Check if workflow ID appears in logs
        const workflowId = result.workflow_id;
        if (workflowId) {
          let foundWorkflowInLogs = false;
          for (const logFile of logFiles) {
            try {
              const logContent = readFileSync(logFile, 'utf8');
              if (logContent.includes(workflowId)) {
                foundWorkflowInLogs = true;
                break;
              }
            } catch (error) {
              // Ignore file read errors
            }
          }

          validations.push({
            name: 'Workflow logged',
            passed: foundWorkflowInLogs,
            error: !foundWorkflowInLogs ? 'Workflow ID not found in logs' : null
          });
        }
      }
    }

    testResult.validations.push(...validations);
  }

  async validateSystemState() {
    console.log('\n🔍 Validating system state...');
    
    // Check for output files
    if (existsSync(TEST_CONFIG.outputDir)) {
      const outputFiles = execSync(`find ${TEST_CONFIG.outputDir} -type f`, { encoding: 'utf8' }).trim().split('\n').filter(Boolean);
      console.log(`📁 Found ${outputFiles.length} output files`);
    }

    // Check for log files
    if (existsSync(TEST_CONFIG.logDir)) {
      const logFiles = execSync(`find ${TEST_CONFIG.logDir} -name "*.log" -type f`, { encoding: 'utf8' }).trim().split('\n').filter(Boolean);
      console.log(`📋 Found ${logFiles.length} log files`);
    }
  }

  generateReport() {
    console.log('\n📊 Test Results Summary');
    console.log('=' .repeat(80));

    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    const totalDuration = Date.now() - this.startTime;

    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests} ✅`);
    console.log(`Failed: ${failedTests} ❌`);
    console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log(`Total Duration: ${totalDuration}ms`);

    // Detailed results
    console.log('\n📋 Detailed Results:');
    this.results.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.name} (${result.duration}ms)`);
      
      if (!result.success) {
        if (result.error) {
          console.log(`   Error: ${result.error}`);
        }
        const failedValidations = result.validations.filter(v => !v.passed);
        failedValidations.forEach(v => {
          console.log(`   - ${v.name}: ${v.error}`);
        });
      }
    });

    // System recommendations
    console.log('\n💡 Recommendations:');
    if (failedTests > 0) {
      console.log('- Review failed test details above');
      console.log('- Check server logs for additional error information');
      console.log('- Verify API keys and environment configuration');
    } else {
      console.log('- All tests passed! System is functioning correctly');
      console.log('- Consider running performance tests for production readiness');
    }

    console.log('\n🎯 Integration test completed!');
    process.exit(failedTests > 0 ? 1 : 0);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new IntegrationTester();
  tester.runAllTests().catch(error => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

export { IntegrationTester, TEST_CASES, TEST_CONFIG };
