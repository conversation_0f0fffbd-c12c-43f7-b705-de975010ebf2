# PostgreSQL with pgvector Setup for Agent Results Persistence

This document provides instructions for setting up PostgreSQL with the pgvector extension to enable persistent storage and semantic search of agent execution results.

## Overview

The agent's result persistence layer has been refactored to use PostgreSQL with pgvector extension, providing:

- **Persistent Storage**: All agent execution results are stored in PostgreSQL
- **Semantic Search**: Vector embeddings enable similarity-based search over historical results
- **Audit Trail**: Complete execution history with step-by-step tracking
- **Performance**: Optimized indexes for both traditional and vector-based queries

## Prerequisites

1. **PostgreSQL 12+** with **pgvector extension**
2. **Node.js 18+** 
3. **OpenAI API Key** (optional, for better embeddings)

## Installation Steps

### 1. Install PostgreSQL and pgvector

#### Ubuntu/Debian:
```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Install pgvector
sudo apt install postgresql-15-pgvector
```

#### macOS:
```bash
# Using Homebrew
brew install postgresql pgvector
```

#### Windows:
1. Install PostgreSQL from https://www.postgresql.org/download/windows/
2. Install pgvector from https://github.com/pgvector/pgvector/releases

### 2. Create Database and User

```sql
-- Connect to PostgreSQL as superuser
sudo -u postgres psql

-- Create database
CREATE DATABASE procure_agent;

-- Create user (replace with your credentials)
CREATE USER your_username WITH PASSWORD 'your_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE procure_agent TO your_username;

-- Connect to the new database
\c procure_agent

-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Exit
\q
```

### 3. Configure Environment Variables

Create or update your `.env` file in the `app` directory:

```env
# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=procure_agent
DATABASE_USER=your_username
DATABASE_PASSWORD=your_password
DATABASE_SSL=false
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10

# Optional: Full connection string (overrides individual settings)
DATABASE_URL=postgresql://your_username:your_password@localhost:5432/procure_agent

# Optional: OpenAI API Key for better embeddings
OPENAI_API_KEY=your_openai_api_key
```

### 4. Install Dependencies

```bash
cd app
npm install pg @types/pg
```

### 5. Run Database Setup

```bash
# Run the automated setup script
npm run db:setup

# Or run individual commands:
npm run db:migrate  # Create tables and indexes
npm run db:seed     # Add test data (optional)
npm run db:info     # View database information
```

## Database Schema

### Main Tables

1. **agent_results**: Stores complete agent execution results with embeddings
2. **agent_execution_steps**: Tracks individual workflow steps
3. **agent_result_tags**: Categorization and filtering tags

### Key Features

- **Vector Embeddings**: 1536-dimensional vectors for semantic search
- **HNSW Indexes**: High-performance vector similarity search
- **JSON Storage**: Flexible storage for workflow data
- **Audit Trail**: Complete execution history with timestamps

## API Endpoints

### Agent Results Management

```http
# Create agent result
POST /api/agent-results

# Get agent result by workflow ID
GET /api/agent-results/{workflowId}

# Update agent result
PATCH /api/agent-results/{workflowId}

# Search results (traditional)
GET /api/agent-results?query=pumps&status=completed&limit=20

# Vector similarity search
POST /api/agent-results/vector-search
{
  "embedding": [0.1, 0.2, ...],
  "similarity_threshold": 0.7,
  "limit": 10
}

# Find similar results by text
POST /api/agent-results/find-similar
{
  "text": "industrial pumps procurement",
  "limit": 10,
  "threshold": 0.7
}

# Get statistics
GET /api/agent-results/stats
```

### Execution Steps

```http
# Create execution step
POST /api/agent-results/{workflowId}/steps

# Update execution step
PATCH /api/agent-results/{workflowId}/steps/{stepId}

# Get execution steps
GET /api/agent-results/{workflowId}/steps
```

### Tags

```http
# Add tag
POST /api/agent-results/{workflowId}/tags

# Get tags
GET /api/agent-results/{workflowId}/tags

# Remove tag
DELETE /api/agent-results/{workflowId}/tags/{tagName}
```

## Usage Examples

### Semantic Search

```javascript
// Find workflows similar to a new query
const response = await fetch('/api/agent-results/find-similar', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    text: 'Find suppliers for industrial equipment in Karnataka',
    limit: 5,
    threshold: 0.7
  })
});

const similarResults = await response.json();
```

### Traditional Search

```javascript
// Search by keywords and filters
const params = new URLSearchParams({
  query: 'pumps',
  status: 'completed',
  region: 'Mangaluru',
  start_date: '2024-01-01',
  limit: '20'
});

const response = await fetch(`/api/agent-results?${params}`);
const results = await response.json();
```

## Maintenance Commands

```bash
# View database information
npm run db:info

# Reset database (WARNING: Deletes all data)
npm run db:reset

# Add test data
npm run db:seed

# Drop all tables
npm run db:drop
```

## Troubleshooting

### Common Issues

1. **pgvector extension not found**
   - Ensure pgvector is installed for your PostgreSQL version
   - Run `CREATE EXTENSION IF NOT EXISTS vector;` as superuser

2. **Connection refused**
   - Check if PostgreSQL is running: `sudo systemctl status postgresql`
   - Verify connection settings in `.env`

3. **Permission denied**
   - Ensure database user has proper privileges
   - Check PostgreSQL authentication settings in `pg_hba.conf`

4. **Embedding generation fails**
   - Check OpenAI API key if using OpenAI embeddings
   - Fallback embeddings will be used if OpenAI is unavailable

### Performance Optimization

1. **Vector Index Tuning**:
   ```sql
   -- Adjust HNSW parameters for your data size
   CREATE INDEX idx_custom_embedding ON agent_results 
   USING hnsw (combined_embedding vector_cosine_ops) 
   WITH (m = 32, ef_construction = 128);
   ```

2. **Connection Pooling**:
   - Adjust `DATABASE_POOL_MIN` and `DATABASE_POOL_MAX` in `.env`
   - Monitor connection usage with `npm run db:info`

## Integration with Agent Workflow

The persistence layer is automatically integrated into the agent workflow:

1. **Initialization**: Creates result record when workflow starts
2. **Step Tracking**: Updates step status and results in real-time
3. **Completion**: Stores final results with embeddings
4. **Error Handling**: Saves partial results even on failure
5. **Auto-tagging**: Automatically categorizes results

## UI Components

The `AgentResultsHistory` component provides:

- **Statistics Dashboard**: Overview of execution metrics
- **Search Interface**: Both traditional and semantic search
- **Results Browser**: Paginated list of historical results
- **Detail Viewer**: Complete result inspection with JSON formatting

## Security Considerations

1. **Database Access**: Use dedicated database user with minimal privileges
2. **Connection Security**: Enable SSL in production (`DATABASE_SSL=true`)
3. **API Security**: Implement authentication for result access endpoints
4. **Data Retention**: Consider implementing data retention policies

## Monitoring

Monitor the system using:

```bash
# Database statistics
npm run db:info

# Application logs
tail -f logs/agent.log

# PostgreSQL logs
sudo tail -f /var/log/postgresql/postgresql-*.log
```

## Backup and Recovery

```bash
# Backup database
pg_dump -h localhost -U your_username procure_agent > backup.sql

# Restore database
psql -h localhost -U your_username procure_agent < backup.sql
```
