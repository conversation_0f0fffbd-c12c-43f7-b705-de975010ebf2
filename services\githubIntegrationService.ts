import { execSync } from 'child_process';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';

interface GitCommitResult {
  success: boolean;
  commitSha?: string;
  commitUrl?: string;
  branch?: string;
  error?: string;
  filesChanged?: string[];
}

interface GitConfig {
  repoPath: string;
  branch: string;
  remoteUrl: string;
  userEmail: string;
  userName: string;
  githubToken?: string;
}

export class GitHubIntegrationService {
  private static instance: GitHubIntegrationService;
  private config: GitConfig;

  constructor(config?: Partial<GitConfig>) {
    this.config = {
      repoPath: process.cwd(),
      branch: 'main',
      remoteUrl: process.env.GITHUB_REPO_URL || 'https://github.com/AymnSh/procureagent.git',
      userEmail: process.env.GIT_USER_EMAIL || '<EMAIL>',
      userName: process.env.GIT_USER_NAME || 'AymnSh',
      githubToken: process.env.GITHUB_TOKEN,
      ...config
    };
  }

  static getInstance(config?: Partial<GitConfig>): GitHubIntegrationService {
    if (!GitHubIntegrationService.instance) {
      GitHubIntegrationService.instance = new GitHubIntegrationService(config);
    }
    return GitHubIntegrationService.instance;
  }

  /**
   * Execute git command with error handling
   */
  private executeGitCommand(command: string, cwd?: string): string {
    try {
      const result = execSync(command, {
        cwd: cwd || this.config.repoPath,
        encoding: 'utf8',
        stdio: ['pipe', 'pipe', 'pipe']
      });
      return result.toString().trim();
    } catch (error: any) {
      console.error(`Git command failed: ${command}`);
      console.error('Error:', error.message);
      throw new Error(`Git command failed: ${error.message}`);
    }
  }

  /**
   * Initialize git repository if needed
   */
  private async initializeRepo(): Promise<void> {
    try {
      // Check if git repo exists
      this.executeGitCommand('git status');
    } catch (error) {
      console.log('🔄 Initializing git repository...');
      this.executeGitCommand('git init');
      this.executeGitCommand(`git remote add origin ${this.config.remoteUrl}`);
    }

    // Configure git user
    try {
      this.executeGitCommand(`git config user.email "${this.config.userEmail}"`);
      this.executeGitCommand(`git config user.name "${this.config.userName}"`);
    } catch (error) {
      console.warn('⚠️ Failed to configure git user:', error);
    }
  }

  /**
   * Save workflow results to files
   */
  async saveWorkflowResults(
    workflowData: {
      input: string;
      queryAnalysis?: string;
      marketResearch?: string;
      recommendations?: string;
      documents?: string;
      actionPlan?: string;
      rfqData?: any;
      timestamp: string;
    }
  ): Promise<string[]> {
    const savedFiles: string[] = [];
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputDir = join(this.config.repoPath, 'workflow-outputs', timestamp);

    try {
      // Create output directory
      if (!existsSync(outputDir)) {
        mkdirSync(outputDir, { recursive: true });
      }

      // Save workflow summary
      const summaryPath = join(outputDir, 'workflow-summary.json');
      writeFileSync(summaryPath, JSON.stringify(workflowData, null, 2));
      savedFiles.push(summaryPath);

      // Save individual components
      if (workflowData.queryAnalysis) {
        const analysisPath = join(outputDir, 'query-analysis.md');
        writeFileSync(analysisPath, workflowData.queryAnalysis);
        savedFiles.push(analysisPath);
      }

      if (workflowData.marketResearch) {
        const researchPath = join(outputDir, 'market-research.md');
        writeFileSync(researchPath, workflowData.marketResearch);
        savedFiles.push(researchPath);
      }

      if (workflowData.recommendations) {
        const recommendationsPath = join(outputDir, 'recommendations.md');
        writeFileSync(recommendationsPath, workflowData.recommendations);
        savedFiles.push(recommendationsPath);
      }

      if (workflowData.documents) {
        const documentsPath = join(outputDir, 'procurement-documents.md');
        writeFileSync(documentsPath, workflowData.documents);
        savedFiles.push(documentsPath);
      }

      if (workflowData.actionPlan) {
        const actionPlanPath = join(outputDir, 'action-plan.md');
        writeFileSync(actionPlanPath, workflowData.actionPlan);
        savedFiles.push(actionPlanPath);
      }

      if (workflowData.rfqData) {
        const rfqPath = join(outputDir, 'generated-rfq.json');
        writeFileSync(rfqPath, JSON.stringify(workflowData.rfqData, null, 2));
        savedFiles.push(rfqPath);
      }

      console.log(`✅ Saved ${savedFiles.length} workflow files to ${outputDir}`);
      return savedFiles.map(file => file.replace(this.config.repoPath + '/', ''));

    } catch (error) {
      console.error('❌ Failed to save workflow results:', error);
      throw error;
    }
  }

  /**
   * Commit and push workflow results to GitHub
   */
  async commitAndPushResults(
    workflowData: any,
    commitMessage?: string
  ): Promise<GitCommitResult> {
    try {
      console.log('🔄 Starting automated git commit and push...');

      // Initialize repo if needed
      await this.initializeRepo();

      // Save workflow results to files
      const savedFiles = await this.saveWorkflowResults(workflowData);

      if (savedFiles.length === 0) {
        return {
          success: false,
          error: 'No files to commit'
        };
      }

      // Stage files
      console.log('📁 Staging files...');
      for (const file of savedFiles) {
        this.executeGitCommand(`git add "${file}"`);
      }

      // Check if there are changes to commit
      try {
        const status = this.executeGitCommand('git status --porcelain');
        if (!status.trim()) {
          console.log('ℹ️ No changes to commit');
          return {
            success: true,
            filesChanged: [],
            branch: this.config.branch
          };
        }
      } catch (error) {
        console.warn('⚠️ Could not check git status, proceeding with commit');
      }

      // Create commit
      const defaultMessage = `Automated procurement workflow results - ${new Date().toISOString()}`;
      const finalCommitMessage = commitMessage || defaultMessage;
      
      console.log('💾 Creating commit...');
      this.executeGitCommand(`git commit -m "${finalCommitMessage}"`);

      // Get commit SHA
      const commitSha = this.executeGitCommand('git rev-parse HEAD');

      // Push to remote (if configured)
      if (this.config.githubToken) {
        console.log('🚀 Pushing to GitHub...');
        
        // Configure remote URL with token
        const authenticatedUrl = this.config.remoteUrl.replace(
          'https://github.com/',
          `https://${this.config.githubToken}@github.com/`
        );
        
        this.executeGitCommand(`git remote set-url origin "${authenticatedUrl}"`);
        this.executeGitCommand(`git push origin ${this.config.branch}`);
        
        // Reset remote URL for security
        this.executeGitCommand(`git remote set-url origin "${this.config.remoteUrl}"`);
      } else {
        console.warn('⚠️ No GitHub token provided, skipping push to remote');
      }

      // Generate GitHub URL
      const repoName = this.config.remoteUrl.split('/').slice(-2).join('/').replace('.git', '');
      const commitUrl = `https://github.com/${repoName}/commit/${commitSha}`;

      console.log('✅ Git operations completed successfully');
      console.log(`📝 Commit SHA: ${commitSha}`);
      console.log(`🔗 Commit URL: ${commitUrl}`);

      return {
        success: true,
        commitSha,
        commitUrl,
        branch: this.config.branch,
        filesChanged: savedFiles
      };

    } catch (error) {
      console.error('❌ Git operations failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Get current git status
   */
  getGitStatus(): { branch: string; hasChanges: boolean; lastCommit?: string } {
    try {
      const branch = this.executeGitCommand('git branch --show-current');
      const status = this.executeGitCommand('git status --porcelain');
      const hasChanges = status.trim().length > 0;
      
      let lastCommit;
      try {
        lastCommit = this.executeGitCommand('git log -1 --format="%H %s"');
      } catch (error) {
        lastCommit = 'No commits found';
      }

      return {
        branch,
        hasChanges,
        lastCommit
      };
    } catch (error) {
      console.error('Failed to get git status:', error);
      return {
        branch: 'unknown',
        hasChanges: false,
        lastCommit: 'Error getting status'
      };
    }
  }

  /**
   * Validate git configuration
   */
  validateConfiguration(): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    // Check if git is available
    try {
      this.executeGitCommand('git --version');
    } catch (error) {
      issues.push('Git is not installed or not available in PATH');
    }

    // Check if repo path exists
    if (!existsSync(this.config.repoPath)) {
      issues.push(`Repository path does not exist: ${this.config.repoPath}`);
    }

    // Check git configuration
    try {
      const userEmail = this.executeGitCommand('git config user.email');
      const userName = this.executeGitCommand('git config user.name');
      
      if (!userEmail) issues.push('Git user email not configured');
      if (!userName) issues.push('Git user name not configured');
    } catch (error) {
      issues.push('Git user configuration not found');
    }

    // Check GitHub token (optional but recommended)
    if (!this.config.githubToken) {
      issues.push('GitHub token not provided - push operations will be skipped');
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }
}
